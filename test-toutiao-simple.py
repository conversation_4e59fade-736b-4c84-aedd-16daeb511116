#!/usr/bin/env python3
"""
今日头条发布功能简化测试
Simple Toutiao Publishing Test

这个脚本专注于测试今日头条适配器的核心功能
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao import ToutiaoAdapter
from textup.models import Content, ContentFormat, TransformedContent


class SimpleToutiaoTest:
    """简化今日头条测试类"""
    
    def __init__(self):
        self.adapter = ToutiaoAdapter()
        self.results = []
        
    def log(self, message, success=True):
        """记录测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
        self.results.append({
            "message": message,
            "success": success,
            "timestamp": datetime.now().isoformat()
        })
    
    async def test_adapter_creation(self):
        """测试适配器创建"""
        try:
            assert self.adapter.platform == "toutiao"
            assert self.adapter.base_url == "https://developer.toutiao.com/api"
            self.log("今日头条适配器创建成功")
            return True
        except Exception as e:
            self.log(f"适配器创建失败: {e}", False)
            return False
    
    async def test_content_transformation(self):
        """测试内容转换"""
        try:
            # 创建测试内容 - 使用正确的字段名
            test_content = Content(
                title="测试文章标题",
                content="# 测试标题\n\n这是一段测试内容，用于验证今日头条适配器的内容转换功能。",
                content_format=ContentFormat.MARKDOWN
            )
            
            # 转换内容
            transformed = await self.adapter.transform_content(test_content)
            
            assert transformed.title == "测试文章标题"
            assert transformed.content_format == ContentFormat.HTML
            assert len(transformed.html) > 0
            
            self.log("内容转换测试通过")
            return True
            
        except Exception as e:
            self.log(f"内容转换失败: {e}", False)
            return False
    
    async def test_content_validation(self):
        """测试内容验证"""
        try:
            # 创建测试内容 - 使用正确的字段名
            valid_content = TransformedContent(
                title="有效标题",
                content="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。",
                content_format=ContentFormat.HTML,
                html="<p>这是一个足够长的内容段落，包含超过100个字符的内容...</p>",
                text="这是一个足够长的内容段落，包含超过100个字符的内容..."
            )
            
            result = self.adapter._validate_format_impl(valid_content)
            self.log(f"内容验证结果: {result.is_valid}")
            
            # 测试无效内容
            invalid_content = TransformedContent(
                title="",  # 空标题
                content="短内容",
                content_format=ContentFormat.HTML,
                html="<p>短内容</p>",
                text="短内容"
            )
            
            invalid_result = self.adapter._validate_format_impl(invalid_content)
            assert not invalid_result.is_valid
            
            self.log("内容验证测试通过")
            return True
            
        except Exception as e:
            self.log(f"内容验证失败: {e}", False)
            return False
    
    async def test_credentials_validation(self):
        """测试凭据验证"""
        try:
            # 测试有效凭据
            valid_creds = {
                "app_id": "1234567890",
                "secret": "abcdef1234567890abcdef1234567890abcd",
                "redirect_uri": "https://example.com/callback"
            }
            
            result = self.adapter._validate_credentials(valid_creds)
            self.log(f"有效凭据验证: {result.is_valid}")
            
            # 测试无效凭据
            invalid_creds = {
                "app_id": "invalid",
                "secret": "short",
                "redirect_uri": "not-a-url"
            }
            
            invalid_result = self.adapter._validate_credentials(invalid_creds)
            assert not invalid_result.is_valid
            
            self.log("凭据验证测试通过")
            return True
            
        except Exception as e:
            self.log(f"凭据验证失败: {e}", False)
            return False
    
    async def test_auth_url_generation(self):
        """测试认证URL生成"""
        try:
            credentials = {
                "app_id": "test_app",
                "secret": "test_secret",
                "redirect_uri": "https://test.com/callback"
            }
            
            auth_url = self.adapter.generate_auth_url(credentials, "test_state")
            assert "client_id=test_app" in auth_url
            assert "state=test_state" in auth_url
            
            self.log(f"认证URL生成成功: {auth_url[:100]}...")
            return True
            
        except Exception as e:
            self.log(f"认证URL生成失败: {e}", False)
            return False
    
    async def test_story_content(self):
        """测试使用历史故事内容"""
        try:
            story_file = project_root / "tests" / "test-story.md"
            if story_file.exists():
                content_text = story_file.read_text(encoding='utf-8')
                
                # 提取标题（第一行去除#号）
                lines = content_text.split('\n')
                title = lines[0].replace('# ', '').strip() if lines else "无标题"
                
                # 创建内容
                story_content = Content(
                    title=title,
                    content=content_text,
                    content_format=ContentFormat.MARKDOWN
                )
                
                # 转换内容
                transformed = await self.adapter.transform_content(story_content)
                
                self.log(f"历史故事内容测试通过: '{title}' -> {len(transformed.html)}字符")
                return True
            else:
                self.log("历史故事文件不存在", False)
                return False
                
        except Exception as e:
            self.log(f"历史故事内容测试失败: {e}", False)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始今日头条功能测试\n")
        print("=" * 50)
        
        tests = [
            self.test_adapter_creation,
            self.test_content_transformation,
            self.test_content_validation,
            self.test_credentials_validation,
            self.test_auth_url_generation,
            self.test_story_content
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if await test():
                passed += 1
            print()
        
        # 生成测试摘要
        print("=" * 50)
        print(f"📊 测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！今日头条适配器功能正常")
        else:
            print(f"⚠️  {total-passed} 个测试失败，需要检查")
        
        return passed == total


async def main():
    """主函数"""
    test = SimpleToutiaoTest()
    success = await test.run_all_tests()
    
    # 保存测试结果
    result_file = project_root / "test-results" / "toutiao-simple-test.json"
    result_file.parent.mkdir(exist_ok=True)
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "results": test.results,
            "success": success
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存: {result_file}")


if __name__ == "__main__":
    asyncio.run(main())