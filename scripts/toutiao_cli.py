#!/usr/bin/env python3
"""
今日头条Playwright适配器命令行工具

提供便捷的命令行接口来使用今日头条Playwright适配器。
"""

import asyncio
import argparse
import json
import re
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, List

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from src.textup.models import TransformedContent, Platform
from src.textup.utils.toutiao_login_helper import To<PERSON>ao<PERSON>oginHelper
from src.textup.utils.cookie_manager import CookieManager
from src.textup.utils.config_manager import load_config


class ToutiaoCLI:
    """今日头条CLI工具类"""

    def __init__(self):
        self.cookie_manager = CookieManager()
        self.login_helper = None
        self.adapter = None
        self.config = None

    async def login_command(self, args):
        """登录命令"""
        print("=== 今日头条登录 ===")
        
        self.login_helper = ToutiaoLoginHelper(
            headless=args.headless,
            timeout=args.timeout * 1000
        )
        
        success = await self.login_helper.login_and_save_cookies(
            account=args.account,
            username=args.username,
            password=args.password,
            force_manual=args.manual
        )
        
        if success:
            print(f"✓ 登录成功！Cookies已保存到账户: {args.account}")
            
            # 验证cookies
            if await self.login_helper.verify_cookies(args.account):
                print("✓ Cookies验证通过")
            else:
                print("✗ Cookies验证失败")
        else:
            print("✗ 登录失败")
            sys.exit(1)

    async def publish_command(self, args):
        """发布命令"""
        print("=== 今日头条文章发布 ===")
        
        # 加载配置
        if args.config:
            self.config = load_config(args.config)
        else:
            self.config = load_config()
        
        # 创建适配器
        self.adapter = ToutiaoPlaywrightAdapter(
            headless=args.headless,
            timeout=args.timeout * 1000,
            max_retries=args.retries
        )
        
        # 准备认证凭证
        cookies_file = self.cookie_manager.get_cookie_file_path("toutiao", args.account)
        credentials = {"cookies_file": cookies_file}
        
        # 读取文章内容
        if args.file:
            content_data = self._read_content_file(args.file)
        else:
            content_data = {
                "title": args.title,
                "content": args.content,
                "tags": args.tags.split(",") if args.tags else []
            }
        
        # 准备发布选项
        options = self._prepare_publish_options(args, content_data)
        
        # 创建内容对象
        content_html = content_data["content"]
        # 简单的HTML到文本转换
        import re
        content_text = re.sub(r'<[^>]+>', '', content_html).strip()

        content = TransformedContent(
            title=content_data["title"],
            content=content_data["content"],
            content_format="html",
            html=content_html,
            text=content_text
        )
        
        try:
            async with self.adapter:
                # 认证
                print("正在认证...")
                auth_result = await self.adapter.authenticate(credentials)
                
                if not auth_result.success:
                    print(f"✗ 认证失败: {auth_result.error_message}")
                    sys.exit(1)
                
                print("✓ 认证成功")
                
                # 验证内容格式
                print("验证内容格式...")
                validation_result = await self.adapter.validate_format(content)
                
                if not validation_result.is_valid:
                    print("✗ 内容格式验证失败:")
                    for error in validation_result.errors:
                        print(f"  - {error.field}: {error.message}")
                    sys.exit(1)
                
                print("✓ 内容格式验证通过")
                
                # 发布文章
                print("正在发布文章...")
                publish_result = await self.adapter.publish(content, options)
                
                if publish_result.success:
                    print("✓ 文章发布成功！")
                    print(f"文章ID: {publish_result.platform_post_id}")
                    if publish_result.published_url:
                        print(f"文章链接: {publish_result.published_url}")
                else:
                    print(f"✗ 文章发布失败: {publish_result.error_message}")
                    if publish_result.error_details:
                        print(f"错误详情: {publish_result.error_details}")
                    sys.exit(1)
                    
        except Exception as e:
            print(f"✗ 发布过程中出现异常: {str(e)}")
            sys.exit(1)

    async def batch_publish_command(self, args):
        """批量发布命令"""
        print("=== 批量发布文章 ===")
        
        # 读取批量文章文件
        articles = self._read_batch_file(args.batch_file)
        
        if not articles:
            print("✗ 未找到有效的文章数据")
            sys.exit(1)
        
        print(f"找到 {len(articles)} 篇文章")
        
        # 创建适配器
        self.adapter = ToutiaoPlaywrightAdapter(
            headless=args.headless,
            timeout=args.timeout * 1000,
            max_retries=args.retries
        )
        
        # 准备认证凭证
        cookies_file = self.cookie_manager.get_cookie_file_path("toutiao", args.account)
        credentials = {"cookies_file": cookies_file}
        
        success_count = 0
        
        try:
            async with self.adapter:
                # 认证
                auth_result = await self.adapter.authenticate(credentials)
                if not auth_result.success:
                    print(f"✗ 认证失败: {auth_result.error_message}")
                    sys.exit(1)
                
                print("✓ 认证成功")
                
                # 逐个发布文章
                for i, article_data in enumerate(articles, 1):
                    print(f"\n[{i}/{len(articles)}] 发布文章: {article_data['title']}")
                    
                    article_html = article_data["content"]
                    article_text = re.sub(r'<[^>]+>', '', article_html).strip()

                    content = TransformedContent(
                        title=article_data["title"],
                        content=article_data["content"],
                        content_format="html",
                        html=article_html,
                        text=article_text
                    )
                    
                    options = {
                        "tags": article_data.get("tags", []),
                        "cover_image": article_data.get("cover_image")
                    }
                    
                    # 设置发布时间（每篇文章间隔指定时间）
                    if args.interval > 0:
                        publish_time = datetime.now() + timedelta(minutes=args.interval * i)
                        options["publish_time"] = publish_time
                        print(f"定时发布时间: {publish_time.strftime('%Y-%m-%d %H:%M')}")
                    
                    result = await self.adapter.publish(content, options)
                    
                    if result.success:
                        print(f"✓ 发布成功")
                        success_count += 1
                    else:
                        print(f"✗ 发布失败: {result.error_message}")
                    
                    # 文章间隔时间
                    if i < len(articles) and args.delay > 0:
                        print(f"等待 {args.delay} 秒后发布下一篇...")
                        await asyncio.sleep(args.delay)
                        
        except Exception as e:
            print(f"✗ 批量发布过程中出现异常: {str(e)}")
        
        print(f"\n批量发布完成，成功发布 {success_count}/{len(articles)} 篇文章")

    async def status_command(self, args):
        """状态检查命令"""
        print("=== 文章状态检查 ===")
        
        self.adapter = ToutiaoPlaywrightAdapter(headless=True)
        cookies_file = self.cookie_manager.get_cookie_file_path("toutiao", args.account)
        credentials = {"cookies_file": cookies_file}
        
        try:
            async with self.adapter:
                auth_result = await self.adapter.authenticate(credentials)
                if not auth_result.success:
                    print(f"✗ 认证失败: {auth_result.error_message}")
                    sys.exit(1)
                
                status = await self.adapter.get_publish_status(args.article_id)
                print(f"文章状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
                
        except Exception as e:
            print(f"✗ 状态检查失败: {str(e)}")

    async def accounts_command(self, args):
        """账户管理命令"""
        print("=== 账户管理 ===")
        
        if args.list:
            accounts = self.cookie_manager.list_accounts("toutiao")
            if accounts:
                print("已保存的账户:")
                for account in accounts:
                    print(f"  - {account}")
            else:
                print("没有已保存的账户")
        
        elif args.delete:
            success = self.cookie_manager.delete_cookies("toutiao", args.delete)
            if success:
                print(f"✓ 已删除账户 {args.delete} 的cookies")
            else:
                print(f"✗ 删除账户 {args.delete} 的cookies失败")
        
        elif args.verify:
            helper = ToutiaoLoginHelper(headless=True)
            is_valid = await helper.verify_cookies(args.verify)
            if is_valid:
                print(f"✓ 账户 {args.verify} 的cookies有效")
            else:
                print(f"✗ 账户 {args.verify} 的cookies已失效")

    def _read_content_file(self, file_path: str) -> dict:
        """读取内容文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    return json.load(f)
                else:
                    # 简单的文本文件格式
                    lines = f.readlines()
                    title = lines[0].strip() if lines else "无标题"
                    content = ''.join(lines[1:]).strip() if len(lines) > 1 else ""
                    return {"title": title, "content": content, "tags": []}
        except Exception as e:
            print(f"✗ 读取内容文件失败: {str(e)}")
            sys.exit(1)

    def _read_batch_file(self, file_path: str) -> List[dict]:
        """读取批量文章文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"✗ 读取批量文章文件失败: {str(e)}")
            return []

    def _prepare_publish_options(self, args, content_data: dict) -> dict:
        """准备发布选项"""
        options = {}
        
        # 标签
        if args.tags:
            options["tags"] = args.tags.split(",")
        elif "tags" in content_data:
            options["tags"] = content_data["tags"]
        
        # 封面图片
        if args.cover:
            options["cover_image"] = args.cover
        elif "cover_image" in content_data:
            options["cover_image"] = content_data["cover_image"]
        
        # 定时发布
        if args.schedule:
            try:
                publish_time = datetime.strptime(args.schedule, "%Y-%m-%d %H:%M")
                options["publish_time"] = publish_time
            except ValueError:
                print(f"✗ 无效的时间格式: {args.schedule}，应为 YYYY-MM-DD HH:MM")
                sys.exit(1)
        
        return options


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="今日头条Playwright适配器命令行工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 登录命令
    login_parser = subparsers.add_parser("login", help="登录今日头条")
    login_parser.add_argument("--account", default="default", help="账户名称")
    login_parser.add_argument("--username", help="用户名（手机号）")
    login_parser.add_argument("--password", help="密码")
    login_parser.add_argument("--manual", action="store_true", help="强制手动登录")
    login_parser.add_argument("--headless", action="store_true", help="无头模式")
    login_parser.add_argument("--timeout", type=int, default=300, help="超时时间（秒）")
    
    # 发布命令
    publish_parser = subparsers.add_parser("publish", help="发布文章")
    publish_parser.add_argument("--account", default="default", help="账户名称")
    publish_parser.add_argument("--title", help="文章标题")
    publish_parser.add_argument("--content", help="文章内容")
    publish_parser.add_argument("--file", help="内容文件路径（JSON或文本）")
    publish_parser.add_argument("--tags", help="标签，用逗号分隔")
    publish_parser.add_argument("--cover", help="封面图片路径")
    publish_parser.add_argument("--schedule", help="定时发布时间 (YYYY-MM-DD HH:MM)")
    publish_parser.add_argument("--config", help="配置文件路径")
    publish_parser.add_argument("--headless", action="store_true", help="无头模式")
    publish_parser.add_argument("--timeout", type=int, default=30, help="超时时间（秒）")
    publish_parser.add_argument("--retries", type=int, default=3, help="重试次数")
    
    # 批量发布命令
    batch_parser = subparsers.add_parser("batch", help="批量发布文章")
    batch_parser.add_argument("batch_file", help="批量文章JSON文件路径")
    batch_parser.add_argument("--account", default="default", help="账户名称")
    batch_parser.add_argument("--interval", type=int, default=10, help="文章间发布间隔（分钟）")
    batch_parser.add_argument("--delay", type=int, default=30, help="操作间延迟（秒）")
    batch_parser.add_argument("--headless", action="store_true", help="无头模式")
    batch_parser.add_argument("--timeout", type=int, default=30, help="超时时间（秒）")
    batch_parser.add_argument("--retries", type=int, default=3, help="重试次数")
    
    # 状态检查命令
    status_parser = subparsers.add_parser("status", help="检查文章状态")
    status_parser.add_argument("article_id", help="文章ID")
    status_parser.add_argument("--account", default="default", help="账户名称")
    
    # 账户管理命令
    accounts_parser = subparsers.add_parser("accounts", help="账户管理")
    accounts_parser.add_argument("--list", action="store_true", help="列出所有账户")
    accounts_parser.add_argument("--delete", help="删除指定账户的cookies")
    accounts_parser.add_argument("--verify", help="验证指定账户的cookies")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = ToutiaoCLI()
    
    try:
        if args.command == "login":
            asyncio.run(cli.login_command(args))
        elif args.command == "publish":
            asyncio.run(cli.publish_command(args))
        elif args.command == "batch":
            asyncio.run(cli.batch_publish_command(args))
        elif args.command == "status":
            asyncio.run(cli.status_command(args))
        elif args.command == "accounts":
            asyncio.run(cli.accounts_command(args))
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"✗ 执行命令时出现异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
