# 今日头条Playwright适配器安装和使用指南

## 项目概述

本项目为您的TextUp项目成功集成了今日头条Playwright适配器，解决了今日头条API不支持文章发布的问题。通过浏览器自动化技术，实现了完整的文章发布功能。

## 🎉 已完成的功能

### ✅ 核心功能
- **Playwright基础适配器**: 通用的浏览器自动化基类
- **今日头条专用适配器**: 针对今日头条平台优化的发布适配器
- **Cookie管理系统**: 安全的登录状态保存和管理
- **登录助手工具**: 支持手动和自动登录
- **配置管理系统**: 灵活的配置文件管理

### ✅ 高级特性
- **智能重试机制**: 多层重试策略，提高发布成功率
- **完善错误处理**: 详细的错误分类和处理
- **图片上传支持**: 封面图片和内容图片自动上传
- **定时发布功能**: 支持设置文章发布时间
- **批量发布支持**: 一次性发布多篇文章
- **反检测机制**: 内置反自动化检测功能

### ✅ 开发工具
- **命令行工具**: 便捷的CLI接口
- **完整测试套件**: 单元测试和集成测试
- **详细文档**: 使用指南和API文档
- **配置示例**: 开箱即用的配置文件

## 📁 新增文件结构

```
src/textup/
├── adapters/
│   ├── playwright_base.py          # Playwright基础适配器
│   └── toutiao_playwright.py       # 今日头条Playwright适配器
└── utils/
    ├── cookie_manager.py           # Cookie管理器
    ├── toutiao_login_helper.py     # 登录助手
    └── config_manager.py           # 配置管理器

config/
└── toutiao_playwright_config.yaml # 配置文件模板

examples/
└── toutiao_playwright_example.py  # 使用示例

scripts/
└── toutiao_cli.py                 # 命令行工具

tests/
└── test_toutiao_playwright_adapter.py # 测试文件

docs/
└── toutiao_playwright_adapter.md  # 详细文档
```

## 🚀 快速开始

### 1. 安装依赖

确保已安装Playwright：
```bash
pip install playwright>=1.40.0
playwright install chromium
```

### 2. 首次登录

使用命令行工具进行登录：
```bash
python scripts/toutiao_cli.py login --account my_account --manual
```

或使用Python代码：
```python
from src.textup.utils.toutiao_login_helper import ToutiaoLoginHelper

async def login():
    helper = ToutiaoLoginHelper(headless=False)
    success = await helper.login_and_save_cookies(
        account="my_account",
        force_manual=True
    )
    return success

import asyncio
asyncio.run(login())
```

### 3. 发布文章

#### 使用命令行工具：
```bash
# 直接发布
python scripts/toutiao_cli.py publish \
    --account my_account \
    --title "我的文章标题" \
    --content "<p>文章内容</p>" \
    --tags "技术,分享"

# 从文件发布
python scripts/toutiao_cli.py publish \
    --account my_account \
    --file article.json

# 批量发布
python scripts/toutiao_cli.py batch articles.json --account my_account
```

#### 使用Python代码：
```python
from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from src.textup.models import TransformedContent, Platform

async def publish_article():
    adapter = ToutiaoPlaywrightAdapter(headless=False)
    
    content = TransformedContent(
        title="我的文章标题",
        content="<p>文章内容</p>",
        platform=Platform.TOUTIAO,
        content_format="html"
    )
    
    credentials = {"cookies_file": "cookies/toutiao_my_account_cookies.json"}
    options = {"tags": ["技术", "分享"]}
    
    async with adapter:
        auth_result = await adapter.authenticate(credentials)
        if auth_result.success:
            result = await adapter.publish(content, options)
            print(f"发布结果: {result.success}")

import asyncio
asyncio.run(publish_article())
```

## 📋 配置说明

### 基本配置文件 (config/toutiao.yaml)
```yaml
browser:
  headless: false
  type: "chromium"

timeouts:
  page_timeout: 30000
  upload_timeout: 60000

retry:
  max_retries: 3
  delay_sequence: [1, 2, 4]

content:
  max_title_length: 100
  max_content_length: 50000
```

### 使用配置文件
```python
from src.textup.utils.config_manager import load_config

config = load_config("config/toutiao.yaml")
adapter = ToutiaoPlaywrightAdapter(
    headless=config.browser.headless,
    timeout=config.timeouts.page_timeout
)
```

## 🔧 集成到现有项目

### 1. 更新适配器注册

在您的适配器注册代码中添加：
```python
from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter

# 注册新的适配器
ADAPTERS = {
    Platform.TOUTIAO: ToutiaoPlaywrightAdapter,  # 替换原有的ToutiaoAdapter
    # ... 其他适配器
}
```

### 2. 更新配置

如果您有全局配置系统，可以添加今日头条Playwright相关配置：
```python
TOUTIAO_CONFIG = {
    "adapter_type": "playwright",
    "headless": False,
    "timeout": 30000,
    "max_retries": 3,
    "cookies_dir": "cookies"
}
```

### 3. 使用示例

```python
async def publish_to_toutiao(title: str, content: str, options: dict = None):
    """发布文章到今日头条"""
    adapter = ToutiaoPlaywrightAdapter()
    
    article_content = TransformedContent(
        title=title,
        content=content,
        platform=Platform.TOUTIAO,
        content_format="html"
    )
    
    credentials = {"cookies_file": "cookies/toutiao_default_cookies.json"}
    
    async with adapter:
        auth_result = await adapter.authenticate(credentials)
        if not auth_result.success:
            raise Exception(f"认证失败: {auth_result.error_message}")
        
        result = await adapter.publish(article_content, options or {})
        if not result.success:
            raise Exception(f"发布失败: {result.error_message}")
        
        return result.platform_post_id
```

## 🧪 测试

运行测试套件：
```bash
# 运行所有测试
python -m pytest tests/test_toutiao_playwright_adapter.py -v

# 运行特定测试
python -m pytest tests/test_toutiao_playwright_adapter.py::TestToutiaoPlaywrightAdapter::test_validate_format_success -v

# 运行集成测试（需要浏览器环境）
python -m pytest tests/test_toutiao_playwright_adapter.py -m integration -v
```

## 📝 使用建议

### 1. 登录管理
- 首次使用建议使用手动登录确保安全性
- 定期验证cookies有效性
- 为不同账户使用不同的cookies文件

### 2. 发布策略
- 控制发布频率，避免被平台限制
- 使用定时发布功能分散发布时间
- 批量发布时适当增加间隔时间

### 3. 错误处理
- 启用详细日志记录便于调试
- 实现自动重试机制
- 保存错误时的截图用于分析

### 4. 性能优化
- 生产环境建议使用headless模式
- 合理设置超时时间
- 复用adapter实例减少初始化开销

## 🔍 故障排除

### 常见问题

**Q: 登录失败怎么办？**
A: 
1. 检查网络连接
2. 清除旧的cookies文件
3. 使用手动登录模式
4. 检查账户是否正常

**Q: 发布失败怎么办？**
A:
1. 检查内容格式
2. 验证图片文件
3. 确认账户权限
4. 查看详细错误信息

**Q: 页面元素找不到怎么办？**
A:
1. 今日头条页面可能更新
2. 调整选择器配置
3. 增加等待时间
4. 启用调试模式

### 调试模式

启用调试获取更多信息：
```python
adapter = ToutiaoPlaywrightAdapter(
    headless=False,
    timeout=60000
)

# 或在配置文件中
debug:
  enabled: true
  verbose_logging: true
  save_screenshots: true
```

## 🎯 下一步

1. **测试验证**: 在您的环境中测试登录和发布功能
2. **配置调整**: 根据需要调整配置参数
3. **集成部署**: 将适配器集成到您的生产环境
4. **监控优化**: 监控发布成功率并持续优化

## 📞 支持

如果您在使用过程中遇到问题，可以：
1. 查看详细文档 `docs/toutiao_playwright_adapter.md`
2. 运行测试套件验证环境
3. 启用调试模式获取更多信息
4. 检查日志文件和截图

---

**恭喜！** 您现在拥有了一个功能完整的今日头条Playwright适配器。这个解决方案不仅解决了API限制问题，还提供了比API更灵活的功能，如图片上传、定时发布等。开始享受自动化发布的便利吧！ 🚀
