"""
TextUp Playwright 平台适配器基类

本模块定义了基于Playwright的平台适配器基础抽象类，用于浏览器自动化发布。
适用于不提供API或API功能受限的平台。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
import asyncio
import json
import os
from datetime import datetime, timedelta
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, Playwright

from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
)
from ..utils import (
    PlatformAdapterProtocol,
    TextUpError,
    PlatformAPIError,
    InvalidCredentialsError,
    TimeoutError,
    handle_exception,
)


class PlaywrightBaseAdapter(PlatformAdapterProtocol, ABC):
    """基于Playwright的平台适配器基类"""

    def __init__(
        self,
        headless: bool = True,
        timeout: int = 30000,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        browser_type: str = "chromium",
        user_data_dir: Optional[str] = None,
        executable_path: Optional[str] = None,
    ):
        """
        初始化Playwright适配器

        Args:
            headless: 是否无头模式运行
            timeout: 页面操作超时时间（毫秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            browser_type: 浏览器类型 (chromium, firefox, webkit)
            user_data_dir: 用户数据目录
            executable_path: 浏览器可执行文件路径
        """
        self.headless = headless
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.browser_type = browser_type
        self.user_data_dir = user_data_dir
        self.executable_path = executable_path

        # Playwright 实例
        self._playwright: Optional[Playwright] = None
        self._browser: Optional[Browser] = None
        self._context: Optional[BrowserContext] = None
        self._page: Optional[Page] = None

        # 认证状态
        self._is_authenticated = False
        self._cookies_file: Optional[str] = None

    @property
    @abstractmethod
    def platform(self) -> Platform:
        """返回平台标识"""
        pass

    @property
    @abstractmethod
    def login_url(self) -> str:
        """返回登录页面URL"""
        pass

    @property
    @abstractmethod
    def publish_url(self) -> str:
        """返回发布页面URL"""
        pass

    @property
    @abstractmethod
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        pass

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_playwright()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._cleanup()

    async def _init_playwright(self):
        """初始化Playwright"""
        if self._playwright is None:
            self._playwright = await async_playwright().start()

        # 获取浏览器类型
        if self.browser_type == "firefox":
            browser_launcher = self._playwright.firefox
        elif self.browser_type == "webkit":
            browser_launcher = self._playwright.webkit
        else:
            browser_launcher = self._playwright.chromium

        # 启动浏览器
        launch_options = {
            "headless": self.headless,
        }
        if self.executable_path:
            launch_options["executable_path"] = self.executable_path

        self._browser = await browser_launcher.launch(**launch_options)

        # 创建浏览器上下文
        context_options = {}
        if self.user_data_dir:
            context_options["storage_state"] = self.user_data_dir

        self._context = await self._browser.new_context(**context_options)
        self._context.set_default_timeout(self.timeout)

        # 设置初始化脚本（反检测）
        await self._set_init_script()

        # 创建页面
        self._page = await self._context.new_page()

    async def _set_init_script(self):
        """设置初始化脚本，用于反检测"""
        init_script = """
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        window.chrome = {
            runtime: {},
        };
        """
        await self._context.add_init_script(init_script)

    async def _cleanup(self):
        """清理资源"""
        if self._page:
            await self._page.close()
            self._page = None

        if self._context:
            await self._context.close()
            self._context = None

        if self._browser:
            await self._browser.close()
            self._browser = None

        if self._playwright:
            await self._playwright.stop()
            self._playwright = None

    async def _save_cookies(self, file_path: str):
        """保存cookies到文件"""
        if self._context:
            await self._context.storage_state(path=file_path)
            self._cookies_file = file_path

    async def _load_cookies(self, file_path: str) -> bool:
        """从文件加载cookies"""
        if not os.path.exists(file_path):
            return False

        try:
            # 重新创建上下文以加载cookies
            if self._context:
                await self._context.close()

            self._context = await self._browser.new_context(storage_state=file_path)
            self._context.set_default_timeout(self.timeout)
            await self._set_init_script()

            if self._page:
                await self._page.close()
            self._page = await self._context.new_page()

            self._cookies_file = file_path
            return True
        except Exception as e:
            handle_exception("load_cookies", e, {"file_path": file_path})
            return False

    async def _check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            await self._page.goto(self.publish_url, wait_until="networkidle")
            return await self._is_logged_in()
        except Exception:
            return False

    @abstractmethod
    async def _is_logged_in(self) -> bool:
        """检查是否已登录（子类实现）"""
        pass

    @abstractmethod
    async def _perform_login(self, credentials: Dict[str, Any]) -> bool:
        """执行登录操作（子类实现）"""
        pass

    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        平台认证

        Args:
            credentials: 认证凭证

        Returns:
            认证结果
        """
        try:
            # 验证凭证格式
            validation = self._validate_credentials(credentials)
            if not validation.is_valid:
                return AuthResult(
                    success=False,
                    platform=self.platform,
                    error_message=f"凭证验证失败: {'; '.join([e.message for e in validation.errors])}",
                )

            # 尝试加载已保存的cookies
            cookies_file = credentials.get("cookies_file")
            if cookies_file and await self._load_cookies(cookies_file):
                if await self._check_login_status():
                    self._is_authenticated = True
                    return AuthResult(
                        success=True,
                        platform=self.platform,
                        message="使用已保存的cookies认证成功",
                    )

            # 执行登录
            login_success = await self._perform_login(credentials)
            if login_success:
                self._is_authenticated = True
                
                # 保存cookies
                if cookies_file:
                    await self._save_cookies(cookies_file)

                return AuthResult(
                    success=True,
                    platform=self.platform,
                    message="登录认证成功",
                )
            else:
                return AuthResult(
                    success=False,
                    platform=self.platform,
                    error_message="登录失败，请检查凭证",
                )

        except Exception as e:
            return AuthResult(
                success=False,
                platform=self.platform,
                error_message=str(e),
                error_details={"exception": type(e).__name__},
            )

    @abstractmethod
    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证"""
        pass

    async def validate_format(self, content: TransformedContent) -> ValidationResult:
        """
        验证内容格式是否符合平台要求

        Args:
            content: 转换后的内容

        Returns:
            验证结果
        """
        try:
            return self._validate_format_impl(content)
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[
                    ValidationError(
                        field="format",
                        message=f"格式验证异常: {str(e)}",
                        value=(
                            content.content_format if hasattr(content, "content_format") else None
                        ),
                    )
                ],
            )

    @abstractmethod
    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """具体的格式验证实现"""
        pass

    async def publish(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
        """
        发布内容到平台

        Args:
            content: 转换后的内容
            options: 发布选项

        Returns:
            发布结果
        """
        try:
            # 验证认证状态
            if not self._is_authenticated:
                return PublishResult(
                    success=False, platform=self.platform, error_message="未认证，请先完成平台认证"
                )

            # 验证内容格式
            validation = await self.validate_format(content)
            if not validation.is_valid:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    error_message=f"内容格式验证失败: {'; '.join([e.message for e in validation.errors])}",
                )

            # 执行具体的发布逻辑
            return await self._publish_impl(content, options)

        except Exception as e:
            return PublishResult(
                success=False,
                platform=self.platform,
                error_message=str(e),
                error_details={
                    "exception": type(e).__name__,
                    "content_title": content.title if hasattr(content, "title") else None,
                },
            )

    @abstractmethod
    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """具体的发布实现"""
        pass

    async def get_publish_status(self, platform_post_id: str) -> Dict[str, Any]:
        """
        获取发布状态

        Args:
            platform_post_id: 平台文章ID

        Returns:
            状态信息字典
        """
        try:
            return await self._get_publish_status_impl(platform_post_id)
        except Exception as e:
            handle_exception("get_publish_status", e, {"platform_post_id": platform_post_id})

    @abstractmethod
    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """具体的状态查询实现"""
        pass
