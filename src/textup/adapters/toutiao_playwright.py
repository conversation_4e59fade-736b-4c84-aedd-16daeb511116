"""
今日头条Playwright适配器

本模块实现了基于Playwright的今日头条平台内容发布功能：
- 浏览器自动化登录
- 文章发布自动化
- 图片上传处理
- 内容格式验证

由于今日头条API不支持文章发布，本适配器使用Playwright模拟浏览器操作。
"""

import asyncio
import json
import os
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

from .playwright_base import PlaywrightBaseAdapter
from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
)
from ..utils import TextUpError, PlatformAPIError, InvalidCredentialsError, handle_exception
from ..utils.cookie_manager import <PERSON>ieManager
from ..utils.toutiao_login_helper import ToutiaoLoginHelper


def retry_on_failure(max_retries: int = 3, delay_sequence: List[float] = None):
    """重试装饰器"""
    if delay_sequence is None:
        delay_sequence = [1, 2, 4]

    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(self, *args, **kwargs)
                except Exception as e:
                    last_exception = e

                    if attempt == max_retries:
                        # 最后一次尝试失败
                        handle_exception(f"{func.__name__}_final_attempt", e, {
                            "attempt": attempt + 1,
                            "max_retries": max_retries
                        })
                        raise e

                    # 计算延迟时间
                    delay = delay_sequence[min(attempt, len(delay_sequence) - 1)]

                    print(f"{func.__name__} 第{attempt + 1}次尝试失败，{delay}秒后重试: {str(e)}")
                    await asyncio.sleep(delay)

            # 理论上不会到达这里
            raise last_exception

        return wrapper
    return decorator


class ToutiaoPlaywrightAdapter(PlaywrightBaseAdapter):
    """今日头条Playwright适配器
    
    使用Playwright自动化浏览器操作来发布文章到今日头条平台。
    支持自动登录、文章发布、图片上传等功能。
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._upload_timeout = 60000  # 上传超时时间（毫秒）
        self._retry_delays = [1, 2, 4, 8]  # 重试延迟序列（秒）
        self._max_publish_retries = 3  # 发布最大重试次数

    @property
    def platform(self) -> Platform:
        """返回平台标识"""
        return Platform.TOUTIAO

    @property
    def login_url(self) -> str:
        """返回登录页面URL"""
        return "https://mp.toutiao.com/"

    @property
    def publish_url(self) -> str:
        """返回发布页面URL"""
        return "https://mp.toutiao.com/profile_v3/graphic/publish"

    @property
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        return ["cookies_file"]  # 主要依赖cookies文件

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证"""
        errors = []
        
        # 检查cookies文件路径
        cookies_file = credentials.get("cookies_file")
        if not cookies_file:
            errors.append(ValidationError(
                field="cookies_file",
                message="cookies文件路径不能为空",
                value=cookies_file
            ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors
        )

    async def _is_logged_in(self) -> bool:
        """检查是否已登录"""
        try:
            # 检查是否存在登录相关的元素
            login_indicators = [
                'text="手机号登录"',
                'text="扫码登录"',
                'text="登录"',
                '.login-container',
                '.qr-code-container'
            ]
            
            for indicator in login_indicators:
                if await self._page.locator(indicator).count() > 0:
                    return False
            
            # 检查是否存在用户信息或发布相关元素
            user_indicators = [
                '.user-info',
                '.avatar',
                'text="发布"',
                'text="草稿"',
                '.publish-btn'
            ]
            
            for indicator in user_indicators:
                if await self._page.locator(indicator).count() > 0:
                    return True
                    
            return False
            
        except Exception as e:
            handle_exception("check_login_status", e)
            return False

    async def _perform_login(self, credentials: Dict[str, Any]) -> bool:
        """执行登录操作"""
        try:
            # 导航到登录页面
            await self._page.goto(self.login_url, wait_until="networkidle")
            
            # 如果提供了用户名和密码，尝试自动登录
            username = credentials.get("username")
            password = credentials.get("password")
            
            if username and password:
                return await self._auto_login(username, password)
            else:
                # 手动登录模式
                return await self._manual_login()
                
        except Exception as e:
            handle_exception("perform_login", e, credentials)
            return False

    async def _auto_login(self, username: str, password: str) -> bool:
        """自动登录"""
        try:
            # 点击手机号登录
            phone_login_btn = self._page.locator('text="手机号登录"')
            if await phone_login_btn.count() > 0:
                await phone_login_btn.click()
                await asyncio.sleep(1)
            
            # 输入用户名
            username_input = self._page.locator('input[placeholder*="手机号"], input[type="tel"]')
            if await username_input.count() > 0:
                await username_input.fill(username)
                await asyncio.sleep(0.5)
            
            # 输入密码
            password_input = self._page.locator('input[type="password"]')
            if await password_input.count() > 0:
                await password_input.fill(password)
                await asyncio.sleep(0.5)
            
            # 点击登录按钮
            login_btn = self._page.locator('button:has-text("登录"), .login-btn')
            if await login_btn.count() > 0:
                await login_btn.click()
                
                # 等待登录完成
                await self._page.wait_for_url("**/profile**", timeout=30000)
                return True
            
            return False
            
        except Exception as e:
            handle_exception("auto_login", e)
            return False

    async def _manual_login(self) -> bool:
        """手动登录模式"""
        try:
            print("请在浏览器中完成登录操作...")
            print("登录完成后，程序将自动继续...")
            
            # 等待用户手动登录
            await self._page.pause()
            
            # 检查是否登录成功
            return await self._is_logged_in()
            
        except Exception as e:
            handle_exception("manual_login", e)
            return False

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """验证内容格式"""
        errors = []
        
        # 检查标题
        if not content.title or len(content.title.strip()) == 0:
            errors.append(ValidationError(
                field="title",
                message="标题不能为空",
                value=content.title
            ))
        elif len(content.title) > 100:
            errors.append(ValidationError(
                field="title",
                message="标题长度不能超过100个字符",
                value=len(content.title)
            ))
        
        # 检查内容
        if not content.content or len(content.content.strip()) == 0:
            errors.append(ValidationError(
                field="content",
                message="内容不能为空",
                value=content.content
            ))
        elif len(content.content) > 50000:
            errors.append(ValidationError(
                field="content",
                message="内容长度不能超过50000个字符",
                value=len(content.content)
            ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors
        )

    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """具体的发布实现"""
        for attempt in range(self._max_publish_retries + 1):
            try:
                # 导航到发布页面
                await self._navigate_to_publish_page()

                # 等待页面加载完成
                await self._wait_for_page_ready()

                # 填写标题
                await self._fill_title_with_retry(content.title)

                # 填写内容
                await self._fill_content_with_retry(content.content)

                # 处理封面图片
                if hasattr(content, 'cover_image') and content.cover_image:
                    await self._upload_cover_image_with_retry(content.cover_image)

                # 处理发布选项
                await self._set_publish_options_with_retry(options)

                # 发布文章
                post_id = await self._submit_article_with_retry()

                return PublishResult(
                    success=True,
                    platform=self.platform,
                    platform_post_id=post_id,
                    message="文章发布成功",
                    published_url=f"https://mp.toutiao.com/profile_v3/graphic/publish?pgc_id={post_id}" if post_id else None
                )

            except Exception as e:
                if attempt == self._max_publish_retries:
                    # 最后一次尝试失败
                    return PublishResult(
                        success=False,
                        platform=self.platform,
                        error_message=f"发布失败（已重试{self._max_publish_retries}次）: {str(e)}",
                        error_details={
                            "exception": type(e).__name__,
                            "attempt": attempt + 1,
                            "max_retries": self._max_publish_retries
                        }
                    )

                # 重试前的延迟
                delay = self._retry_delays[min(attempt, len(self._retry_delays) - 1)]
                print(f"发布第{attempt + 1}次尝试失败，{delay}秒后重试: {str(e)}")
                await asyncio.sleep(delay)

                # 重新初始化页面
                try:
                    await self._page.reload(wait_until="networkidle")
                except Exception:
                    # 如果重新加载失败，尝试重新导航
                    await self._page.goto(self.publish_url, wait_until="networkidle")

    async def _navigate_to_publish_page(self):
        """导航到发布页面"""
        try:
            await self._page.goto(self.publish_url, wait_until="networkidle", timeout=30000)
        except PlaywrightTimeoutError:
            # 如果超时，尝试重新加载
            await self._page.reload(wait_until="domcontentloaded")

    async def _wait_for_page_ready(self):
        """等待页面准备就绪"""
        try:
            # 等待关键元素出现
            key_selectors = [
                'input[placeholder*="标题"]',
                '.ql-editor',
                '.editor-content',
                'div[contenteditable="true"]'
            ]

            for selector in key_selectors:
                try:
                    await self._page.wait_for_selector(selector, timeout=10000)
                    break
                except PlaywrightTimeoutError:
                    continue

            # 额外等待时间确保页面完全加载
            await asyncio.sleep(2)

        except Exception as e:
            handle_exception("wait_for_page_ready", e)

    @retry_on_failure(max_retries=2, delay_sequence=[1, 2])
    async def _fill_title_with_retry(self, title: str):
        """带重试的填写标题"""
        return await self._fill_title(title)

    @retry_on_failure(max_retries=2, delay_sequence=[1, 2])
    async def _fill_content_with_retry(self, content: str):
        """带重试的填写内容"""
        return await self._fill_content(content)

    @retry_on_failure(max_retries=2, delay_sequence=[2, 4])
    async def _upload_cover_image_with_retry(self, image_path: str):
        """带重试的上传封面图片"""
        return await self._upload_cover_image(image_path)

    @retry_on_failure(max_retries=1, delay_sequence=[1])
    async def _set_publish_options_with_retry(self, options: Dict[str, Any]):
        """带重试的设置发布选项"""
        return await self._set_publish_options(options)

    @retry_on_failure(max_retries=2, delay_sequence=[2, 4])
    async def _submit_article_with_retry(self) -> Optional[str]:
        """带重试的提交文章"""
        return await self._submit_article()

    async def _fill_title(self, title: str):
        """填写文章标题"""
        try:
            # 查找标题输入框
            title_selectors = [
                'input[placeholder*="标题"]',
                '.title-input input',
                'input[name="title"]',
                '.article-title input'
            ]
            
            for selector in title_selectors:
                title_input = self._page.locator(selector)
                if await title_input.count() > 0:
                    await title_input.fill(title)
                    await asyncio.sleep(0.5)
                    return
            
            raise Exception("未找到标题输入框")
            
        except Exception as e:
            raise Exception(f"填写标题失败: {str(e)}")

    async def _fill_content(self, content: str):
        """填写文章内容"""
        try:
            # 预处理内容
            processed_content = await self._preprocess_content(content)

            # 查找内容编辑器
            content_selectors = [
                '.ql-editor',
                '.editor-content',
                '.content-editor',
                'div[contenteditable="true"]',
                'textarea[placeholder*="内容"]',
                '.ProseMirror'
            ]

            for selector in content_selectors:
                content_editor = self._page.locator(selector)
                if await content_editor.count() > 0:
                    await content_editor.click()
                    await asyncio.sleep(0.5)

                    # 清空现有内容
                    await self._page.keyboard.press("Control+A")
                    await self._page.keyboard.press("Delete")
                    await asyncio.sleep(0.3)

                    # 分段输入内容（避免内容过长导致问题）
                    await self._input_content_by_chunks(content_editor, processed_content)
                    await asyncio.sleep(1)
                    return

            raise Exception("未找到内容编辑器")

        except Exception as e:
            raise Exception(f"填写内容失败: {str(e)}")

    async def _preprocess_content(self, content: str) -> str:
        """预处理内容"""
        try:
            # 移除不支持的HTML标签
            import re

            # 移除script和style标签
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 移除iframe标签
            content = re.sub(r'<iframe[^>]*>.*?</iframe>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 处理链接（今日头条可能不支持外链）
            content = re.sub(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>', r'\2', content)

            # 处理图片标签，提取图片信息用于后续上传
            # 这里先保留img标签，后续在上传时处理

            return content.strip()

        except Exception as e:
            handle_exception("preprocess_content", e)
            return content

    async def _input_content_by_chunks(self, editor, content: str, chunk_size: int = 1000):
        """分块输入内容"""
        try:
            # 如果内容较短，直接输入
            if len(content) <= chunk_size:
                await editor.fill(content)
                return

            # 分块输入
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]

                if i == 0:
                    await editor.fill(chunk)
                else:
                    # 追加内容
                    await editor.focus()
                    await self._page.keyboard.press("Control+End")  # 移动到末尾
                    await self._page.keyboard.type(chunk)

                await asyncio.sleep(0.2)  # 短暂延迟

        except Exception as e:
            # 如果分块输入失败，尝试直接输入
            await editor.fill(content)

    async def _upload_cover_image(self, image_path: str):
        """上传封面图片"""
        try:
            if not os.path.exists(image_path):
                raise Exception(f"封面图片文件不存在: {image_path}")

            # 验证图片格式和大小
            if not await self._validate_image(image_path):
                raise Exception("图片格式或大小不符合要求")

            # 查找封面上传按钮
            cover_selectors = [
                'text="选择封面"',
                'text="设置封面"',
                '.cover-upload',
                '.upload-cover',
                'button:has-text("封面")'
            ]

            for selector in cover_selectors:
                cover_btn = self._page.locator(selector)
                if await cover_btn.count() > 0:
                    await cover_btn.click()
                    await asyncio.sleep(1)

                    # 等待上传对话框出现
                    await self._page.wait_for_selector('.upload-modal, .cover-modal, .semi-modal', timeout=5000)

                    # 查找文件上传输入框
                    file_input_selectors = [
                        'input[type="file"][accept*="image"]',
                        '.upload-input input[type="file"]',
                        'input[type="file"]'
                    ]

                    for input_selector in file_input_selectors:
                        file_input = self._page.locator(input_selector)
                        if await file_input.count() > 0:
                            await file_input.set_input_files(image_path)

                            # 等待上传完成
                            await self._wait_for_upload_complete()

                            # 查找确认按钮
                            confirm_selectors = [
                                'button:has-text("确定")',
                                'button:has-text("完成")',
                                'button:has-text("保存")',
                                '.confirm-btn'
                            ]

                            for confirm_selector in confirm_selectors:
                                confirm_btn = self._page.locator(confirm_selector)
                                if await confirm_btn.count() > 0:
                                    await confirm_btn.click()
                                    await asyncio.sleep(1)
                                    break

                            return

                    break

            print("警告: 未找到封面上传功能，跳过封面设置")

        except Exception as e:
            print(f"警告: 上传封面图片失败: {str(e)}")

    async def _validate_image(self, image_path: str) -> bool:
        """验证图片格式和大小"""
        try:
            # 检查文件扩展名
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_ext = Path(image_path).suffix.lower()

            if file_ext not in valid_extensions:
                print(f"不支持的图片格式: {file_ext}")
                return False

            # 检查文件大小（限制为10MB）
            file_size = os.path.getsize(image_path)
            max_size = 10 * 1024 * 1024  # 10MB

            if file_size > max_size:
                print(f"图片文件过大: {file_size / 1024 / 1024:.2f}MB > 10MB")
                return False

            return True

        except Exception as e:
            handle_exception("validate_image", e, {"image_path": image_path})
            return False

    async def _wait_for_upload_complete(self, timeout: int = 30):
        """等待上传完成"""
        try:
            # 等待上传进度条消失或成功提示出现
            success_indicators = [
                '.upload-success',
                'text="上传成功"',
                '.success-icon'
            ]

            progress_indicators = [
                '.upload-progress',
                '.progress-bar',
                'text="上传中"'
            ]

            # 等待上传开始
            await asyncio.sleep(1)

            # 等待上传完成
            for i in range(timeout):
                # 检查是否上传成功
                for indicator in success_indicators:
                    if await self._page.locator(indicator).count() > 0:
                        return True

                # 检查是否还在上传中
                still_uploading = False
                for indicator in progress_indicators:
                    if await self._page.locator(indicator).count() > 0:
                        still_uploading = True
                        break

                if not still_uploading:
                    # 没有进度指示器，可能已完成
                    await asyncio.sleep(1)
                    return True

                await asyncio.sleep(1)

            print("警告: 上传超时")
            return False

        except Exception as e:
            handle_exception("wait_for_upload_complete", e)
            return False

    async def _upload_content_images(self, content: str) -> str:
        """上传内容中的图片"""
        try:
            import re

            # 查找所有img标签
            img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
            img_matches = re.finditer(img_pattern, content)

            updated_content = content

            for match in img_matches:
                img_tag = match.group(0)
                img_src = match.group(1)

                # 如果是本地文件路径，上传图片
                if os.path.exists(img_src):
                    uploaded_url = await self._upload_single_image(img_src)
                    if uploaded_url:
                        # 替换图片URL
                        new_img_tag = img_tag.replace(img_src, uploaded_url)
                        updated_content = updated_content.replace(img_tag, new_img_tag)

            return updated_content

        except Exception as e:
            handle_exception("upload_content_images", e)
            return content

    async def _upload_single_image(self, image_path: str) -> Optional[str]:
        """上传单个图片并返回URL"""
        try:
            if not await self._validate_image(image_path):
                return None

            # 在编辑器中插入图片
            # 这需要根据今日头条编辑器的具体实现来调整

            # 查找图片上传按钮
            image_upload_selectors = [
                '.image-upload-btn',
                'button[title*="图片"]',
                '.toolbar .image-btn',
                'text="插入图片"'
            ]

            for selector in image_upload_selectors:
                upload_btn = self._page.locator(selector)
                if await upload_btn.count() > 0:
                    await upload_btn.click()
                    await asyncio.sleep(1)

                    # 上传文件
                    file_input = self._page.locator('input[type="file"][accept*="image"]')
                    if await file_input.count() > 0:
                        await file_input.set_input_files(image_path)

                        # 等待上传完成并获取URL
                        await self._wait_for_upload_complete()

                        # 这里需要根据实际情况获取上传后的图片URL
                        # 暂时返回原路径，实际使用时需要调整
                        return image_path

            return None

        except Exception as e:
            handle_exception("upload_single_image", e, {"image_path": image_path})
            return None

    async def _set_publish_options(self, options: Dict[str, Any]):
        """设置发布选项"""
        try:
            # 设置发布时间
            publish_time = options.get("publish_time")
            if publish_time:
                await self._set_publish_time(publish_time)
            
            # 设置标签
            tags = options.get("tags", [])
            if tags:
                await self._set_tags(tags)
            
            # 其他选项...
            
        except Exception as e:
            print(f"警告: 设置发布选项失败: {str(e)}")

    async def _set_publish_time(self, publish_time: datetime):
        """设置发布时间"""
        try:
            # 查找定时发布选项
            schedule_selectors = [
                'text="定时发布"',
                '.schedule-publish',
                'input[type="radio"][value="schedule"]'
            ]
            
            for selector in schedule_selectors:
                schedule_option = self._page.locator(selector)
                if await schedule_option.count() > 0:
                    await schedule_option.click()
                    await asyncio.sleep(1)
                    
                    # 设置时间
                    time_str = publish_time.strftime("%Y-%m-%d %H:%M")
                    time_input = self._page.locator('input[placeholder*="时间"], .time-picker input')
                    if await time_input.count() > 0:
                        await time_input.fill(time_str)
                        await self._page.keyboard.press("Enter")
                    
                    return
            
        except Exception as e:
            print(f"警告: 设置发布时间失败: {str(e)}")

    async def _set_tags(self, tags: List[str]):
        """设置文章标签"""
        try:
            # 查找标签输入框
            tag_selectors = [
                '.tag-input',
                'input[placeholder*="标签"]',
                '.tags-container input'
            ]
            
            for selector in tag_selectors:
                tag_input = self._page.locator(selector)
                if await tag_input.count() > 0:
                    for tag in tags[:5]:  # 限制标签数量
                        await tag_input.fill(f"#{tag}")
                        await self._page.keyboard.press("Space")
                        await asyncio.sleep(0.5)
                    return
            
        except Exception as e:
            print(f"警告: 设置标签失败: {str(e)}")

    async def _submit_article(self) -> Optional[str]:
        """提交发布文章"""
        try:
            # 发布前检查
            await self._pre_publish_check()

            # 查找发布按钮
            publish_btn = await self._find_publish_button()
            if not publish_btn:
                raise Exception("未找到发布按钮")

            # 检查按钮是否可点击
            if not await publish_btn.is_enabled():
                raise Exception("发布按钮不可点击，请检查内容是否完整")

            # 点击发布
            await publish_btn.click()

            # 等待发布结果
            return await self._wait_for_publish_result()

        except Exception as e:
            raise Exception(f"提交文章失败: {str(e)}")

    async def _pre_publish_check(self):
        """发布前检查"""
        try:
            # 检查标题是否已填写
            title_selectors = [
                'input[placeholder*="标题"]',
                '.title-input input',
                'input[name="title"]'
            ]

            title_filled = False
            for selector in title_selectors:
                title_input = self._page.locator(selector)
                if await title_input.count() > 0:
                    title_value = await title_input.input_value()
                    if title_value and title_value.strip():
                        title_filled = True
                        break

            if not title_filled:
                raise Exception("标题未填写")

            # 检查内容是否已填写
            content_selectors = [
                '.ql-editor',
                '.editor-content',
                'div[contenteditable="true"]'
            ]

            content_filled = False
            for selector in content_selectors:
                content_editor = self._page.locator(selector)
                if await content_editor.count() > 0:
                    content_text = await content_editor.text_content()
                    if content_text and content_text.strip():
                        content_filled = True
                        break

            if not content_filled:
                raise Exception("内容未填写")

        except Exception as e:
            raise Exception(f"发布前检查失败: {str(e)}")

    async def _find_publish_button(self):
        """查找发布按钮"""
        publish_selectors = [
            'button:has-text("发布")',
            'button:has-text("立即发布")',
            '.publish-btn',
            'button[type="submit"]',
            '.submit-btn',
            'button.primary:has-text("发布")'
        ]

        for selector in publish_selectors:
            publish_btn = self._page.locator(selector)
            if await publish_btn.count() > 0:
                return publish_btn

        return None

    async def _wait_for_publish_result(self, timeout: int = 30) -> Optional[str]:
        """等待发布结果"""
        try:
            # 等待页面跳转或成功提示
            success_indicators = [
                "**/manage**",
                "**/success**",
                "**/published**"
            ]

            # 尝试等待URL变化
            for indicator in success_indicators:
                try:
                    await self._page.wait_for_url(indicator, timeout=timeout * 1000)

                    # 尝试从URL中提取文章ID
                    current_url = self._page.url
                    match = re.search(r'pgc_id=(\d+)', current_url)
                    if match:
                        return match.group(1)

                    return "published"

                except PlaywrightTimeoutError:
                    continue

            # 如果URL没有变化，检查页面上的成功提示
            success_message_selectors = [
                'text="发布成功"',
                'text="发表成功"',
                '.success-message',
                '.publish-success'
            ]

            for selector in success_message_selectors:
                if await self._page.locator(selector).count() > 0:
                    return "published"

            # 检查是否有错误提示
            await self._check_publish_errors()

            # 如果没有明确的成功或失败提示，可能需要更长时间
            print("等待发布完成...")
            await asyncio.sleep(5)

            # 再次检查
            if await self._page.locator('text="发布成功"').count() > 0:
                return "published"

            return "published"  # 假设发布成功

        except Exception as e:
            raise Exception(f"等待发布结果失败: {str(e)}")

    async def _check_publish_errors(self):
        """检查发布错误"""
        error_selectors = [
            '.error-message',
            '.alert-error',
            '.publish-error',
            'text="发布失败"',
            'text="发表失败"',
            'text="网络错误"',
            'text="系统错误"'
        ]

        for selector in error_selectors:
            error_element = self._page.locator(selector)
            if await error_element.count() > 0:
                error_text = await error_element.text_content()
                raise Exception(f"发布错误: {error_text}")

    async def _handle_publish_error(self, error_message: str) -> bool:
        """处理发布错误"""
        try:
            # 根据错误类型采取不同的处理策略
            if "网络" in error_message or "超时" in error_message:
                print("网络错误，等待后重试...")
                await asyncio.sleep(3)
                return True  # 可以重试

            elif "标题" in error_message:
                print("标题相关错误，需要检查标题内容")
                return False  # 不建议重试

            elif "内容" in error_message:
                print("内容相关错误，需要检查内容格式")
                return False  # 不建议重试

            elif "权限" in error_message or "认证" in error_message:
                print("权限错误，需要重新登录")
                return False  # 不建议重试

            else:
                print(f"未知错误: {error_message}")
                return True  # 可以尝试重试

        except Exception as e:
            handle_exception("handle_publish_error", e, {"error_message": error_message})
            return False

    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """获取发布状态"""
        try:
            # 导航到文章管理页面
            manage_url = "https://mp.toutiao.com/profile_v3/graphic/manage"
            await self._page.goto(manage_url, wait_until="networkidle")
            
            # 查找对应的文章
            # 这里需要根据实际页面结构来实现
            
            return {
                "status": "published",
                "platform_post_id": platform_post_id,
                "message": "文章已发布"
            }
            
        except Exception as e:
            return {
                "status": "unknown",
                "platform_post_id": platform_post_id,
                "error": str(e)
            }
