"""
TextUp 适配器工厂

本模块提供适配器工厂类，用于根据平台和适配器类型创建相应的适配器实例。
支持多种适配器类型：API适配器和Playwright自动化适配器。
"""

from enum import Enum
from typing import Dict, Any, Type, Optional
from ..models import Platform
from ..utils import TextUpError
from .base import BaseAdapter


class AdapterType(str, Enum):
    """适配器类型枚举"""
    API = "api"
    PLAYWRIGHT = "playwright"
    AUTO = "auto"  # 自动选择最佳适配器


class AdapterFactory:
    """适配器工厂类"""

    # 适配器注册表
    _adapters: Dict[Platform, Dict[AdapterType, Type[BaseAdapter]]] = {}

    @classmethod
    def register_adapter(
        self,
        platform: Platform,
        adapter_type: AdapterType,
        adapter_class: Type[BaseAdapter]
    ):
        """
        注册适配器

        Args:
            platform: 平台
            adapter_type: 适配器类型
            adapter_class: 适配器类
        """
        if platform not in self._adapters:
            self._adapters[platform] = {}

        self._adapters[platform][adapter_type] = adapter_class

    @classmethod
    def create_adapter(
        self,
        platform: Platform,
        adapter_type: AdapterType = AdapterType.AUTO,
        **kwargs
    ) -> BaseAdapter:
        """
        创建适配器实例

        Args:
            platform: 目标平台
            adapter_type: 适配器类型
            **kwargs: 适配器初始化参数

        Returns:
            适配器实例

        Raises:
            TextUpError: 适配器创建失败
        """
        if platform not in self._adapters:
            raise TextUpError(f"不支持的平台: {platform}")

        platform_adapters = self._adapters[platform]

        # 自动选择适配器
        if adapter_type == AdapterType.AUTO:
            adapter_type = self._select_best_adapter(platform)

        if adapter_type not in platform_adapters:
            available_types = list(platform_adapters.keys())
            raise TextUpError(
                f"平台 {platform} 不支持适配器类型 {adapter_type}，"
                f"可用类型: {', '.join(available_types)}"
            )

        adapter_class = platform_adapters[adapter_type]

        try:
            return adapter_class(**kwargs)
        except Exception as e:
            raise TextUpError(f"创建 {platform} 适配器失败: {str(e)}")

    @classmethod
    def _select_best_adapter(self, platform: Platform) -> AdapterType:
        """
        自动选择最佳适配器类型

        Args:
            platform: 平台

        Returns:
            推荐的适配器类型
        """
        platform_adapters = self._adapters.get(platform, {})

        # 知乎和今日头条平台只支持Playwright（因为没有公开API或API不支持发布）
        if platform in [Platform.ZHIHU, Platform.TOUTIAO]:
            if AdapterType.PLAYWRIGHT in platform_adapters:
                return AdapterType.PLAYWRIGHT

        # 其他平台优先选择API
        else:
            if AdapterType.API in platform_adapters:
                return AdapterType.API
            elif AdapterType.PLAYWRIGHT in platform_adapters:
                return AdapterType.PLAYWRIGHT

        # 返回第一个可用的适配器
        if platform_adapters:
            return list(platform_adapters.keys())[0]

        raise TextUpError(f"平台 {platform} 没有可用的适配器")

    @classmethod
    def get_available_adapters(self, platform: Optional[Platform] = None) -> Dict[str, Any]:
        """
        获取可用的适配器信息

        Args:
            platform: 指定平台，如果为None则返回所有平台

        Returns:
            适配器信息字典
        """
        if platform:
            if platform not in self._adapters:
                return {}

            return {
                "platform": platform.value,
                "adapters": {
                    adapter_type.value: {
                        "class": adapter_class.__name__,
                        "module": adapter_class.__module__
                    }
                    for adapter_type, adapter_class in self._adapters[platform].items()
                }
            }

        # 返回所有平台的适配器信息
        result = {}
        for plt, adapters in self._adapters.items():
            result[plt.value] = {
                "platform": plt.value,
                "adapters": {
                    adapter_type.value: {
                        "class": adapter_class.__name__,
                        "module": adapter_class.__module__
                    }
                    for adapter_type, adapter_class in adapters.items()
                }
            }

        return result

    @classmethod
    def is_adapter_available(self, platform: Platform, adapter_type: AdapterType) -> bool:
        """
        检查特定适配器是否可用

        Args:
            platform: 平台
            adapter_type: 适配器类型

        Returns:
            是否可用
        """
        return (
            platform in self._adapters and
            adapter_type in self._adapters[platform]
        )


# 注册默认适配器
def register_default_adapters():
    """注册默认适配器"""
    try:
        # 知乎适配器 - 只使用Playwright实现，因为知乎没有公开的HTTP API
        from .zhihu import ZhihuAdapter
        from .zhihu_playwright import ZhihuPlaywrightAdapter

        # 注册Playwright适配器
        AdapterFactory.register_adapter(
            Platform.ZHIHU,
            AdapterType.PLAYWRIGHT,
            ZhihuPlaywrightAdapter
        )

        # 为了向后兼容，也注册API适配器（实际上是Playwright的别名）
        AdapterFactory.register_adapter(
            Platform.ZHIHU,
            AdapterType.API,
            ZhihuAdapter
        )

    except ImportError:
        pass  # 适配器模块不存在时跳过

    try:
        # 微博适配器
        from .weibo import WeiboAdapter
        AdapterFactory.register_adapter(
            Platform.WEIBO,
            AdapterType.API,
            WeiboAdapter
        )
    except ImportError:
        pass

    try:
        # 头条适配器 - 使用Playwright实现
        from .toutiao_playwright import ToutiaoPlaywrightAdapter
        from .toutiao import ToutiaoAdapter  # 向后兼容别名

        # 注册Playwright适配器作为主要实现
        AdapterFactory.register_adapter(
            Platform.TOUTIAO,
            AdapterType.PLAYWRIGHT,
            ToutiaoPlaywrightAdapter
        )

        # 为了向后兼容，也注册API适配器（实际上是Playwright的别名）
        AdapterFactory.register_adapter(
            Platform.TOUTIAO,
            AdapterType.API,
            ToutiaoAdapter
        )
    except ImportError:
        pass


# 自动注册默认适配器
register_default_adapters()


def create_adapter(
    platform: str,
    adapter_type: str = "auto",
    **kwargs
) -> BaseAdapter:
    """
    便捷函数：创建适配器

    Args:
        platform: 平台名称（字符串）
        adapter_type: 适配器类型（字符串）
        **kwargs: 适配器参数

    Returns:
        适配器实例
    """
    try:
        platform_enum = Platform(platform.lower())
        adapter_type_enum = AdapterType(adapter_type.lower())

        return AdapterFactory.create_adapter(
            platform_enum,
            adapter_type_enum,
            **kwargs
        )
    except ValueError as e:
        raise TextUpError(f"无效的参数: {str(e)}")


def get_recommended_adapter_type(platform: str) -> str:
    """
    获取推荐的适配器类型

    Args:
        platform: 平台名称

    Returns:
        推荐的适配器类型
    """
    try:
        platform_enum = Platform(platform.lower())
        recommended = AdapterFactory._select_best_adapter(platform_enum)
        return recommended.value
    except ValueError:
        raise TextUpError(f"不支持的平台: {platform}")
