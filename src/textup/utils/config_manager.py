"""
配置管理器

本模块提供配置文件的加载、验证和管理功能。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field

from ..utils import handle_exception


@dataclass
class BrowserConfig:
    """浏览器配置"""
    type: str = "chromium"
    headless: bool = False
    executable_path: Optional[str] = None
    user_data_dir: Optional[str] = None


@dataclass
class TimeoutConfig:
    """超时配置"""
    page_timeout: int = 30000
    upload_timeout: int = 60000
    login_timeout: int = 300000


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    delay_sequence: list = field(default_factory=lambda: [1, 2, 4, 8])
    max_publish_retries: int = 3


@dataclass
class CookieConfig:
    """Cookie配置"""
    storage_dir: str = "cookies"
    max_age_days: int = 30
    auto_cleanup: bool = True


@dataclass
class ContentConfig:
    """内容配置"""
    max_title_length: int = 100
    max_content_length: int = 50000
    supported_image_formats: list = field(default_factory=lambda: [".jpg", ".jpeg", ".png", ".gif", ".webp"])
    max_image_size_mb: int = 10
    max_tags: int = 5


@dataclass
class PublishConfig:
    """发布配置"""
    interval_between_posts: int = 30
    auto_set_cover: bool = True
    default_tags: list = field(default_factory=list)
    enable_scheduled_publish: bool = True


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    save_screenshots: bool = True
    screenshot_dir: str = "screenshots"
    verbose_browser_logs: bool = False


@dataclass
class ToutiaoPlaywrightConfig:
    """今日头条Playwright适配器配置"""
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    timeouts: TimeoutConfig = field(default_factory=TimeoutConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    cookies: CookieConfig = field(default_factory=CookieConfig)
    content: ContentConfig = field(default_factory=ContentConfig)
    publish: PublishConfig = field(default_factory=PublishConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    platform: Dict[str, Any] = field(default_factory=dict)
    error_handling: Dict[str, Any] = field(default_factory=dict)
    performance: Dict[str, Any] = field(default_factory=dict)
    security: Dict[str, Any] = field(default_factory=dict)
    debug: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._config: Optional[ToutiaoPlaywrightConfig] = None

    def load_config(self, config_file: Optional[str] = None) -> ToutiaoPlaywrightConfig:
        """
        加载配置文件

        Args:
            config_file: 配置文件路径

        Returns:
            配置对象
        """
        try:
            if config_file:
                self.config_file = config_file

            if not self.config_file:
                # 使用默认配置
                self._config = ToutiaoPlaywrightConfig()
                return self._config

            config_path = Path(self.config_file)
            if not config_path.exists():
                print(f"配置文件不存在: {self.config_file}，使用默认配置")
                self._config = ToutiaoPlaywrightConfig()
                return self._config

            # 读取YAML配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 解析配置
            self._config = self._parse_config(config_data)
            return self._config

        except Exception as e:
            handle_exception("load_config", e, {"config_file": config_file})
            # 返回默认配置
            self._config = ToutiaoPlaywrightConfig()
            return self._config

    def _parse_config(self, config_data: Dict[str, Any]) -> ToutiaoPlaywrightConfig:
        """解析配置数据"""
        try:
            # 解析浏览器配置
            browser_data = config_data.get("browser", {})
            browser_config = BrowserConfig(
                type=browser_data.get("type", "chromium"),
                headless=browser_data.get("headless", False),
                executable_path=browser_data.get("executable_path"),
                user_data_dir=browser_data.get("user_data_dir")
            )

            # 解析超时配置
            timeout_data = config_data.get("timeouts", {})
            timeout_config = TimeoutConfig(
                page_timeout=timeout_data.get("page_timeout", 30000),
                upload_timeout=timeout_data.get("upload_timeout", 60000),
                login_timeout=timeout_data.get("login_timeout", 300000)
            )

            # 解析重试配置
            retry_data = config_data.get("retry", {})
            retry_config = RetryConfig(
                max_retries=retry_data.get("max_retries", 3),
                delay_sequence=retry_data.get("delay_sequence", [1, 2, 4, 8]),
                max_publish_retries=retry_data.get("max_publish_retries", 3)
            )

            # 解析Cookie配置
            cookie_data = config_data.get("cookies", {})
            cookie_config = CookieConfig(
                storage_dir=cookie_data.get("storage_dir", "cookies"),
                max_age_days=cookie_data.get("max_age_days", 30),
                auto_cleanup=cookie_data.get("auto_cleanup", True)
            )

            # 解析内容配置
            content_data = config_data.get("content", {})
            content_config = ContentConfig(
                max_title_length=content_data.get("max_title_length", 100),
                max_content_length=content_data.get("max_content_length", 50000),
                supported_image_formats=content_data.get("supported_image_formats", [".jpg", ".jpeg", ".png", ".gif", ".webp"]),
                max_image_size_mb=content_data.get("max_image_size_mb", 10),
                max_tags=content_data.get("max_tags", 5)
            )

            # 解析发布配置
            publish_data = config_data.get("publish", {})
            publish_config = PublishConfig(
                interval_between_posts=publish_data.get("interval_between_posts", 30),
                auto_set_cover=publish_data.get("auto_set_cover", True),
                default_tags=publish_data.get("default_tags", []),
                enable_scheduled_publish=publish_data.get("enable_scheduled_publish", True)
            )

            # 解析日志配置
            logging_data = config_data.get("logging", {})
            logging_config = LoggingConfig(
                level=logging_data.get("level", "INFO"),
                save_screenshots=logging_data.get("save_screenshots", True),
                screenshot_dir=logging_data.get("screenshot_dir", "screenshots"),
                verbose_browser_logs=logging_data.get("verbose_browser_logs", False)
            )

            # 创建完整配置对象
            config = ToutiaoPlaywrightConfig(
                browser=browser_config,
                timeouts=timeout_config,
                retry=retry_config,
                cookies=cookie_config,
                content=content_config,
                publish=publish_config,
                logging=logging_config,
                platform=config_data.get("platform", {}),
                error_handling=config_data.get("error_handling", {}),
                performance=config_data.get("performance", {}),
                security=config_data.get("security", {}),
                debug=config_data.get("debug", {})
            )

            return config

        except Exception as e:
            handle_exception("parse_config", e, {"config_data": config_data})
            return ToutiaoPlaywrightConfig()

    def get_config(self) -> ToutiaoPlaywrightConfig:
        """获取当前配置"""
        if self._config is None:
            return self.load_config()
        return self._config

    def save_config(self, config: ToutiaoPlaywrightConfig, config_file: Optional[str] = None) -> bool:
        """
        保存配置到文件

        Args:
            config: 配置对象
            config_file: 配置文件路径

        Returns:
            是否保存成功
        """
        try:
            if config_file:
                self.config_file = config_file

            if not self.config_file:
                print("未指定配置文件路径")
                return False

            # 转换为字典
            config_data = self._config_to_dict(config)

            # 确保目录存在
            config_path = Path(self.config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存到YAML文件
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)

            return True

        except Exception as e:
            handle_exception("save_config", e, {"config_file": config_file})
            return False

    def _config_to_dict(self, config: ToutiaoPlaywrightConfig) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        return {
            "browser": {
                "type": config.browser.type,
                "headless": config.browser.headless,
                "executable_path": config.browser.executable_path,
                "user_data_dir": config.browser.user_data_dir
            },
            "timeouts": {
                "page_timeout": config.timeouts.page_timeout,
                "upload_timeout": config.timeouts.upload_timeout,
                "login_timeout": config.timeouts.login_timeout
            },
            "retry": {
                "max_retries": config.retry.max_retries,
                "delay_sequence": config.retry.delay_sequence,
                "max_publish_retries": config.retry.max_publish_retries
            },
            "cookies": {
                "storage_dir": config.cookies.storage_dir,
                "max_age_days": config.cookies.max_age_days,
                "auto_cleanup": config.cookies.auto_cleanup
            },
            "content": {
                "max_title_length": config.content.max_title_length,
                "max_content_length": config.content.max_content_length,
                "supported_image_formats": config.content.supported_image_formats,
                "max_image_size_mb": config.content.max_image_size_mb,
                "max_tags": config.content.max_tags
            },
            "publish": {
                "interval_between_posts": config.publish.interval_between_posts,
                "auto_set_cover": config.publish.auto_set_cover,
                "default_tags": config.publish.default_tags,
                "enable_scheduled_publish": config.publish.enable_scheduled_publish
            },
            "logging": {
                "level": config.logging.level,
                "save_screenshots": config.logging.save_screenshots,
                "screenshot_dir": config.logging.screenshot_dir,
                "verbose_browser_logs": config.logging.verbose_browser_logs
            },
            "platform": config.platform,
            "error_handling": config.error_handling,
            "performance": config.performance,
            "security": config.security,
            "debug": config.debug
        }

    def update_config(self, updates: Dict[str, Any]) -> bool:
        """
        更新配置

        Args:
            updates: 更新的配置项

        Returns:
            是否更新成功
        """
        try:
            if self._config is None:
                self.load_config()

            # 这里可以实现更复杂的配置更新逻辑
            # 暂时简单处理
            for key, value in updates.items():
                if hasattr(self._config, key):
                    setattr(self._config, key, value)

            return True

        except Exception as e:
            handle_exception("update_config", e, {"updates": updates})
            return False

    def validate_config(self, config: Optional[ToutiaoPlaywrightConfig] = None) -> bool:
        """
        验证配置有效性

        Args:
            config: 要验证的配置对象

        Returns:
            配置是否有效
        """
        try:
            if config is None:
                config = self.get_config()

            # 验证浏览器类型
            valid_browsers = ["chromium", "firefox", "webkit"]
            if config.browser.type not in valid_browsers:
                print(f"无效的浏览器类型: {config.browser.type}")
                return False

            # 验证超时时间
            if config.timeouts.page_timeout <= 0:
                print("页面超时时间必须大于0")
                return False

            # 验证重试配置
            if config.retry.max_retries < 0:
                print("最大重试次数不能为负数")
                return False

            # 验证内容限制
            if config.content.max_title_length <= 0:
                print("标题最大长度必须大于0")
                return False

            if config.content.max_content_length <= 0:
                print("内容最大长度必须大于0")
                return False

            return True

        except Exception as e:
            handle_exception("validate_config", e)
            return False


# 全局配置管理器实例
_config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    return _config_manager


def load_config(config_file: Optional[str] = None) -> ToutiaoPlaywrightConfig:
    """加载配置的便捷函数"""
    return _config_manager.load_config(config_file)
