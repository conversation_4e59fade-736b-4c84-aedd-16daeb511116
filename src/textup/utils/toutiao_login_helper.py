"""
今日头条登录助手

本模块提供今日头条平台的登录辅助功能，包括自动登录和手动登录引导。
"""

import asyncio
import os
from typing import Dict, Any, Optional
from datetime import datetime

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from .cookie_manager import CookieManager
from ..utils import handle_exception


class ToutiaoLoginHelper:
    """今日头条登录助手"""

    def __init__(
        self,
        headless: bool = False,
        timeout: int = 30000,
        executable_path: Optional[str] = None
    ):
        """
        初始化登录助手

        Args:
            headless: 是否无头模式
            timeout: 超时时间（毫秒）
            executable_path: 浏览器可执行文件路径
        """
        self.headless = headless
        self.timeout = timeout
        self.executable_path = executable_path
        self.cookie_manager = CookieManager()

    async def login_and_save_cookies(
        self,
        account: str = "default",
        username: Optional[str] = None,
        password: Optional[str] = None,
        force_manual: bool = False
    ) -> bool:
        """
        登录并保存cookies

        Args:
            account: 账户名称
            username: 用户名（手机号）
            password: 密码
            force_manual: 强制手动登录

        Returns:
            是否登录成功
        """
        async with async_playwright() as playwright:
            try:
                # 启动浏览器
                launch_options = {"headless": self.headless}
                if self.executable_path:
                    launch_options["executable_path"] = self.executable_path

                browser = await playwright.chromium.launch(**launch_options)
                context = await browser.new_context()
                context.set_default_timeout(self.timeout)

                # 设置反检测脚本
                await self._set_init_script(context)

                page = await context.new_page()

                # 导航到登录页面
                await page.goto("https://mp.toutiao.com/", wait_until="networkidle")

                # 检查是否已经登录
                if await self._check_login_status(page):
                    print("检测到已登录状态，保存cookies...")
                    await self._save_cookies(context, account)
                    return True

                # 执行登录
                login_success = False
                if not force_manual and username and password:
                    print("尝试自动登录...")
                    login_success = await self._auto_login(page, username, password)

                if not login_success:
                    print("开始手动登录流程...")
                    login_success = await self._manual_login(page)

                if login_success:
                    print("登录成功，保存cookies...")
                    await self._save_cookies(context, account)
                    return True
                else:
                    print("登录失败")
                    return False

            except Exception as e:
                handle_exception("login_and_save_cookies", e, {
                    "account": account,
                    "username": username is not None
                })
                return False
            finally:
                if 'browser' in locals():
                    await browser.close()

    async def _set_init_script(self, context: BrowserContext):
        """设置反检测脚本"""
        init_script = """
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        window.chrome = {
            runtime: {},
        };
        
        // 隐藏自动化特征
        delete navigator.__proto__.webdriver;
        """
        await context.add_init_script(init_script)

    async def _check_login_status(self, page: Page) -> bool:
        """检查登录状态"""
        try:
            # 等待页面加载
            await asyncio.sleep(2)

            # 检查登录相关元素
            login_indicators = [
                'text="手机号登录"',
                'text="扫码登录"',
                'text="登录"',
                '.login-container',
                '.qr-code-container'
            ]

            for indicator in login_indicators:
                if await page.locator(indicator).count() > 0:
                    return False

            # 检查用户信息元素
            user_indicators = [
                '.user-info',
                '.avatar',
                'text="发布"',
                'text="草稿"',
                '.publish-btn',
                '.user-name'
            ]

            for indicator in user_indicators:
                if await page.locator(indicator).count() > 0:
                    return True

            return False

        except Exception as e:
            handle_exception("check_login_status", e)
            return False

    async def _auto_login(self, page: Page, username: str, password: str) -> bool:
        """自动登录"""
        try:
            # 点击手机号登录
            phone_login_btn = page.locator('text="手机号登录"')
            if await phone_login_btn.count() > 0:
                await phone_login_btn.click()
                await asyncio.sleep(1)

            # 输入手机号
            username_selectors = [
                'input[placeholder*="手机号"]',
                'input[type="tel"]',
                'input[name="username"]',
                '.phone-input input'
            ]

            username_filled = False
            for selector in username_selectors:
                username_input = page.locator(selector)
                if await username_input.count() > 0:
                    await username_input.fill(username)
                    await asyncio.sleep(0.5)
                    username_filled = True
                    break

            if not username_filled:
                print("未找到用户名输入框")
                return False

            # 输入密码
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                '.password-input input'
            ]

            password_filled = False
            for selector in password_selectors:
                password_input = page.locator(selector)
                if await password_input.count() > 0:
                    await password_input.fill(password)
                    await asyncio.sleep(0.5)
                    password_filled = True
                    break

            if not password_filled:
                print("未找到密码输入框")
                return False

            # 点击登录按钮
            login_selectors = [
                'button:has-text("登录")',
                '.login-btn',
                'button[type="submit"]',
                '.submit-btn'
            ]

            for selector in login_selectors:
                login_btn = page.locator(selector)
                if await login_btn.count() > 0:
                    await login_btn.click()
                    break

            # 等待登录结果
            try:
                await page.wait_for_url("**/profile**", timeout=15000)
                return True
            except:
                # 检查是否有验证码或其他验证
                if await page.locator('text="验证码"').count() > 0:
                    print("需要验证码，转为手动登录模式")
                    return False

                # 检查是否有错误提示
                error_selectors = [
                    '.error-message',
                    '.alert-error',
                    'text="登录失败"',
                    'text="用户名或密码错误"'
                ]

                for selector in error_selectors:
                    if await page.locator(selector).count() > 0:
                        error_text = await page.locator(selector).text_content()
                        print(f"登录错误: {error_text}")
                        return False

                return False

        except Exception as e:
            handle_exception("auto_login", e)
            return False

    async def _manual_login(self, page: Page) -> bool:
        """手动登录"""
        try:
            print("\n" + "="*50)
            print("请在浏览器中完成登录操作")
            print("支持以下登录方式：")
            print("1. 手机号 + 密码")
            print("2. 手机号 + 验证码")
            print("3. 扫码登录")
            print("登录完成后，程序将自动检测并继续...")
            print("="*50 + "\n")

            # 等待用户手动登录
            max_wait_time = 300  # 5分钟
            check_interval = 2   # 2秒检查一次

            for i in range(0, max_wait_time, check_interval):
                await asyncio.sleep(check_interval)

                if await self._check_login_status(page):
                    print("检测到登录成功！")
                    return True

                if i % 30 == 0:  # 每30秒提示一次
                    remaining = max_wait_time - i
                    print(f"等待登录中... (剩余 {remaining} 秒)")

            print("登录超时，请重试")
            return False

        except Exception as e:
            handle_exception("manual_login", e)
            return False

    async def _save_cookies(self, context: BrowserContext, account: str):
        """保存cookies"""
        try:
            # 获取storage state
            storage_state = await context.storage_state()

            # 保存到cookie管理器
            success = self.cookie_manager.save_cookies("toutiao", storage_state, account)

            if success:
                print(f"Cookies已保存到账户: {account}")
            else:
                print("保存cookies失败")

        except Exception as e:
            handle_exception("save_cookies", e, {"account": account})

    async def verify_cookies(self, account: str = "default") -> bool:
        """验证cookies有效性"""
        try:
            # 加载cookies
            cookies_data = self.cookie_manager.load_cookies("toutiao", account)
            if not cookies_data:
                print(f"账户 {account} 的cookies不存在或已过期")
                return False

            async with async_playwright() as playwright:
                # 启动浏览器
                launch_options = {"headless": True}
                if self.executable_path:
                    launch_options["executable_path"] = self.executable_path

                browser = await playwright.chromium.launch(**launch_options)
                context = await browser.new_context(storage_state=cookies_data)
                context.set_default_timeout(self.timeout)

                page = await context.new_page()

                # 访问发布页面
                await page.goto("https://mp.toutiao.com/profile_v3/graphic/publish", 
                              wait_until="networkidle")

                # 检查登录状态
                is_valid = await self._check_login_status(page)

                await browser.close()

                if is_valid:
                    print(f"账户 {account} 的cookies有效")
                else:
                    print(f"账户 {account} 的cookies已失效")

                return is_valid

        except Exception as e:
            handle_exception("verify_cookies", e, {"account": account})
            return False

    def list_saved_accounts(self) -> list:
        """列出已保存的账户"""
        return self.cookie_manager.list_accounts("toutiao")

    def delete_account_cookies(self, account: str) -> bool:
        """删除账户cookies"""
        return self.cookie_manager.delete_cookies("toutiao", account)

    def backup_account_cookies(self, account: str) -> Optional[str]:
        """备份账户cookies"""
        return self.cookie_manager.backup_cookies("toutiao", account)


# 命令行工具函数
async def main():
    """命令行主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="今日头条登录助手")
    parser.add_argument("--account", default="default", help="账户名称")
    parser.add_argument("--username", help="用户名（手机号）")
    parser.add_argument("--password", help="密码")
    parser.add_argument("--manual", action="store_true", help="强制手动登录")
    parser.add_argument("--verify", action="store_true", help="验证cookies有效性")
    parser.add_argument("--list", action="store_true", help="列出已保存的账户")
    parser.add_argument("--delete", help="删除指定账户的cookies")
    parser.add_argument("--headless", action="store_true", help="无头模式运行")

    args = parser.parse_args()

    helper = ToutiaoLoginHelper(headless=args.headless)

    if args.list:
        accounts = helper.list_saved_accounts()
        if accounts:
            print("已保存的账户:")
            for account in accounts:
                print(f"  - {account}")
        else:
            print("没有已保存的账户")
        return

    if args.delete:
        success = helper.delete_account_cookies(args.delete)
        if success:
            print(f"已删除账户 {args.delete} 的cookies")
        else:
            print(f"删除账户 {args.delete} 的cookies失败")
        return

    if args.verify:
        is_valid = await helper.verify_cookies(args.account)
        return

    # 执行登录
    success = await helper.login_and_save_cookies(
        account=args.account,
        username=args.username,
        password=args.password,
        force_manual=args.manual
    )

    if success:
        print("登录成功！")
    else:
        print("登录失败！")


if __name__ == "__main__":
    asyncio.run(main())
