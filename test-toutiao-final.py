#!/usr/bin/env python3
"""
今日头条发布功能最终测试
Final Toutiao Publishing Test

这个脚本测试今日头条适配器的完整功能，包括实际发布流程
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao import ToutiaoAdapter
from textup.models import Content, ContentFormat, TransformedContent


class ToutiaoFinalTest:
    """今日头条最终测试类"""
    
    def __init__(self):
        self.adapter = ToutiaoAdapter()
        self.results = []
        
    def log(self, message, success=True):
        """记录测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
        self.results.append({
            "message": message,
            "success": success,
            "timestamp": datetime.now().isoformat()
        })
    
    async def test_adapter_creation(self):
        """测试适配器创建"""
        try:
            assert self.adapter.platform == "toutiao"
            assert self.adapter.base_url == "https://developer.toutiao.com/api"
            assert self.adapter.oauth_base_url == "https://developer.toutiao.com/oauth"
            self.log("今日头条适配器创建成功")
            return True
        except Exception as e:
            self.log(f"适配器创建失败: {e}", False)
            return False
    
    async def test_content_transformation(self):
        """测试内容转换"""
        try:
            # 创建测试内容
            test_content = Content(
                title="测试文章标题",
                content="# 测试标题\n\n这是一段**加粗**的测试内容，用于验证今日头条适配器的内容转换功能。",
                content_format=ContentFormat.MARKDOWN
            )
            
            # 转换内容
            transformed = await self.adapter.transform_content(test_content)
            
            assert transformed.title == "测试文章标题"
            assert "<h1>测试标题</h1>" in transformed.html  # 检查HTML字段而非content字段
            assert "<strong>加粗</strong>" in transformed.html  # 检查HTML字段而非content字段
            
            self.log(f"内容转换测试通过: {len(transformed.html)}字符")
            return True
            
        except Exception as e:
            self.log(f"内容转换失败: {e}", False)
            return False
    
    async def test_content_validation(self):
        """测试内容验证"""
        try:
            # 创建有效的TransformedContent对象
            valid_content = TransformedContent(
                title="有效标题",
                content="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。",
                content_format=ContentFormat.HTML,
                html="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。",
                text="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。"
            )
            
            result = self.adapter._validate_format_impl(valid_content)
            self.log(f"内容验证结果: {result.is_valid}")
            
            # 测试无效内容
            invalid_content = TransformedContent(
                title="",  # 空标题
                content="短内容",
                content_format=ContentFormat.HTML,
                html="短内容",
                text="短内容"
            )
            
            invalid_result = self.adapter._validate_format_impl(invalid_content)
            
            assert not invalid_result.is_valid
            self.log("内容验证测试通过")
            return True
            
        except Exception as e:
            self.log(f"内容验证失败: {e}", False)
            return False
    
    async def test_credentials_validation(self):
        """测试凭据验证"""
        try:
            # 测试有效凭据
            valid_creds = {
                "app_id": "1234567890",
                "secret": "abcdef1234567890abcdef1234567890abcd",
                "redirect_uri": "https://example.com/callback"
            }
            
            result = self.adapter._validate_credentials(valid_creds)
            self.log(f"有效凭据验证: {result.is_valid}")
            
            # 测试各种无效凭据
            test_cases = [
                ({}, "空凭据"),
                ({"app_id": ""}, "空app_id"),
                ({"app_id": "123", "secret": "short", "redirect_uri": "invalid"}, "格式错误"),
                ({"app_id": "abc123", "secret": "toolongsecretkey", "redirect_uri": "http://valid.com"}, "有效格式"),
            ]
            
            for creds, desc in test_cases:
                result = self.adapter._validate_credentials(creds)
                self.log(f"凭据测试 '{desc}': {result.is_valid}")
            
            self.log("凭据验证测试通过")
            return True
            
        except Exception as e:
            self.log(f"凭据验证失败: {e}", False)
            return False
    
    async def test_auth_url_generation(self):
        """测试认证URL生成"""
        try:
            credentials = {
                "app_id": "test_app_12345",
                "secret": "test_secret_key_32chars_long_enough",
                "redirect_uri": "https://myapp.com/callback"
            }
            
            auth_url = self.adapter.generate_auth_url(credentials, "test_state_123")
            
            # 验证URL包含必要参数
            required_params = [
                "client_id=test_app_12345",
                "redirect_uri=https%3A%2F%2Fmyapp.com%2Fcallback",
                "state=test_state_123",
                "response_type=code",
                "scope=article.publish"
            ]
            
            for param in required_params:
                assert param in auth_url, f"缺少参数: {param}"
            
            self.log(f"认证URL生成成功: {len(auth_url)}字符")
            return True
            
        except Exception as e:
            self.log(f"认证URL生成失败: {e}", False)
            return False
    
    async def test_story_content_processing(self):
        """测试历史故事内容处理"""
        try:
            story_file = project_root / "tests" / "test-story.md"
            if not story_file.exists():
                self.log("历史故事文件不存在", False)
                return False
            
            content_text = story_file.read_text(encoding='utf-8')
            
            # 提取标题
            lines = content_text.split('\n')
            title = "唐朝茶圣的秘密"  # 已知标题
            
            # 创建内容
            story_content = Content(
                title=title,
                content=content_text,
                content_format=ContentFormat.MARKDOWN
            )
            
            # 转换内容
            transformed = await self.adapter.transform_content(story_content)
            
            # 验证转换结果
            assert transformed.title == title
            assert len(transformed.html) > 1000  # 应该有大量HTML内容
            assert "<h1>" in transformed.html  # 应该有HTML标题
            
            self.log(f"历史故事处理成功: '{title}' ({len(transformed.html)}字符)")
            
            # 验证内容是否符合今日头条要求
            validation_result = self.adapter._validate_format_impl(transformed)
            self.log(f"故事内容验证: {validation_result.is_valid}")
            
            return True
            
        except Exception as e:
            self.log(f"历史故事处理失败: {e}", False)
            return False
    
    async def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        try:
            print("\n🔄 开始端到端工作流测试...")
            
            # 1. 加载内容
            story_file = project_root / "tests" / "test-story.md"
            content_text = story_file.read_text(encoding='utf-8')
            lines = content_text.split('\n')
            title = "唐朝茶圣的秘密"
            
            content = Content(
                title=title,
                content=content_text,
                content_format=ContentFormat.MARKDOWN
            )
            
            # 2. 转换内容
            transformed = await self.adapter.transform_content(content)
            self.log("✅ 内容转换完成")
            
            # 3. 验证内容
            validation = self.adapter._validate_format_impl(transformed)
            if not validation.is_valid:
                errors = [e.message for e in validation.errors]
                self.log(f"内容验证失败: {', '.join(errors)}", False)
                return False
            self.log("✅ 内容验证通过")
            
            # 4. 生成认证URL（模拟OAuth流程）
            credentials = {
                "app_id": "demo_app_id",
                "secret": "demo_secret_key_32chars_long_enough_for_demo",
                "redirect_uri": "https://demo-app.com/callback"
            }
            
            auth_url = self.adapter.generate_auth_url(credentials)
            self.log("✅ OAuth认证URL生成完成")
            
            # 5. 模拟发布准备
            self.log("📋 发布准备完成:")
            self.log(f"   标题: {transformed.title}")
            self.log(f"   内容长度: {len(transformed.content)}字符")
            self.log(f"   认证URL: {auth_url[:80]}...")
            
            return True
            
        except Exception as e:
            self.log(f"端到端工作流失败: {e}", False)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始今日头条完整功能测试\n")
        print("=" * 60)
        
        tests = [
            ("适配器创建", self.test_adapter_creation),
            ("内容转换", self.test_content_transformation),
            ("内容验证", self.test_content_validation),
            ("凭据验证", self.test_credentials_validation),
            ("认证URL生成", self.test_auth_url_generation),
            ("历史故事处理", self.test_story_content_processing),
            ("端到端工作流", self.test_end_to_end_workflow),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            print("-" * 40)
            
            if await test_func():
                passed += 1
            
            await asyncio.sleep(0.5)  # 短暂延迟以便阅读输出
        
        # 生成测试摘要
        print("\n" + "=" * 60)
        print(f"📊 测试完成统计")
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {total - passed}")
        print(f"通过率: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 恭喜！所有今日头条功能测试通过")
            print("✅ 今日头条适配器已准备就绪，可以开始实际发布测试")
        else:
            print(f"\n⚠️  有 {total - passed} 个测试失败，需要修复后再进行实际发布")
        
        return passed == total


async def main():
    """主函数"""
    test = ToutiaoFinalTest()
    success = await test.run_all_tests()
    
    # 保存详细测试结果
    result_file = project_root / "test-results" / "toutiao-final-test.json"
    result_file.parent.mkdir(exist_ok=True)
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "summary": {
                "total_tests": len(test.results),
                "passed": sum(1 for r in test.results if r["success"]),
                "failed": sum(1 for r in test.results if not r["success"])
            },
            "details": test.results
        }, f, ensure_ascii=False, indent=2)
    
    # 生成用户友好的报告
    report_file = project_root / "test-results" / "toutiao-test-report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 今日头条发布功能测试报告\n\n")
        f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**测试结果**: {'✅ 全部通过' if success else '❌ 部分失败'}\n\n")
        
        f.write("## 测试项目\n\n")
        for result in test.results:
            emoji = "✅" if result["success"] else "❌"
            f.write(f"{emoji} {result['message']}\n")
    
    print(f"\n📄 详细报告已保存:")
    print(f"   JSON报告: {result_file}")
    print(f"   Markdown报告: {report_file}")
    
    # 提供下一步建议
    print("\n🎯 下一步操作建议:")
    if success:
        print("1. 获取今日头条开发者账号和API凭据")
        print("2. 更新配置文件中的app_id和secret")
        print("3. 运行实际发布测试")
    else:
        print("1. 检查失败的测试项目")
        print("2. 修复适配器中的兼容性问题")
        print("3. 重新运行测试")


if __name__ == "__main__":
    asyncio.run(main())