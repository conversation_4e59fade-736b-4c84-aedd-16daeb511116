#!/usr/bin/env python3
"""
今日头条Playwright适配器测试

本测试文件包含对ToutiaoPlaywrightAdapter的各种功能测试。
"""

import asyncio
import os
import pytest
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from src.textup.models import TransformedContent, Platform, ValidationResult, ValidationError
from src.textup.utils.cookie_manager import CookieManager
from src.textup.utils.toutiao_login_helper import ToutiaoLoginHelper
from src.textup.utils.config_manager import ConfigManager, ToutiaoPlaywrightConfig


class TestToutiaoPlaywrightAdapter:
    """今日头条Playwright适配器测试类"""

    @pytest.fixture
    def adapter(self):
        """创建适配器实例"""
        return ToutiaoPlaywrightAdapter(
            headless=True,
            timeout=10000,
            max_retries=1
        )

    @pytest.fixture
    def sample_content(self):
        """示例内容"""
        return TransformedContent(
            title="测试文章标题",
            content="<p>这是测试文章内容</p>",
            platform=Platform.TOUTIAO,
            content_format="html"
        )

    @pytest.fixture
    def sample_credentials(self):
        """示例认证凭证"""
        return {
            "cookies_file": "test_cookies.json"
        }

    def test_adapter_initialization(self):
        """测试适配器初始化"""
        adapter = ToutiaoPlaywrightAdapter(
            headless=True,
            timeout=20000,
            max_retries=2
        )
        
        assert adapter.headless is True
        assert adapter.timeout == 20000
        assert adapter.max_retries == 2
        assert adapter.platform == Platform.TOUTIAO
        assert adapter.login_url == "https://mp.toutiao.com/"
        assert adapter.publish_url == "https://mp.toutiao.com/profile_v3/graphic/publish"

    def test_required_credentials(self, adapter):
        """测试必需的认证字段"""
        required = adapter.required_credentials
        assert "cookies_file" in required

    def test_validate_credentials_success(self, adapter):
        """测试认证凭证验证成功"""
        credentials = {"cookies_file": "test.json"}
        result = adapter._validate_credentials(credentials)
        
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_credentials_failure(self, adapter):
        """测试认证凭证验证失败"""
        credentials = {}  # 缺少cookies_file
        result = adapter._validate_credentials(credentials)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert result.errors[0].field == "cookies_file"

    def test_validate_format_success(self, adapter, sample_content):
        """测试内容格式验证成功"""
        result = adapter._validate_format_impl(sample_content)
        
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_format_empty_title(self, adapter):
        """测试空标题验证失败"""
        content = TransformedContent(
            title="",
            content="<p>内容</p>",
            platform=Platform.TOUTIAO
        )
        
        result = adapter._validate_format_impl(content)
        
        assert result.is_valid is False
        assert any(error.field == "title" for error in result.errors)

    def test_validate_format_empty_content(self, adapter):
        """测试空内容验证失败"""
        content = TransformedContent(
            title="标题",
            content="",
            platform=Platform.TOUTIAO
        )
        
        result = adapter._validate_format_impl(content)
        
        assert result.is_valid is False
        assert any(error.field == "content" for error in result.errors)

    def test_validate_format_title_too_long(self, adapter):
        """测试标题过长验证失败"""
        long_title = "a" * 101  # 超过100字符限制
        content = TransformedContent(
            title=long_title,
            content="<p>内容</p>",
            platform=Platform.TOUTIAO
        )
        
        result = adapter._validate_format_impl(content)
        
        assert result.is_valid is False
        assert any(error.field == "title" for error in result.errors)

    def test_validate_format_content_too_long(self, adapter):
        """测试内容过长验证失败"""
        long_content = "a" * 50001  # 超过50000字符限制
        content = TransformedContent(
            title="标题",
            content=long_content,
            platform=Platform.TOUTIAO
        )
        
        result = adapter._validate_format_impl(content)
        
        assert result.is_valid is False
        assert any(error.field == "content" for error in result.errors)

    @patch('os.path.exists')
    def test_validate_image_success(self, mock_exists, adapter):
        """测试图片验证成功"""
        mock_exists.return_value = True
        
        with patch('os.path.getsize', return_value=1024 * 1024):  # 1MB
            result = asyncio.run(adapter._validate_image("test.jpg"))
            assert result is True

    @patch('os.path.exists')
    def test_validate_image_not_exists(self, mock_exists, adapter):
        """测试图片文件不存在"""
        mock_exists.return_value = False
        
        result = asyncio.run(adapter._validate_image("nonexistent.jpg"))
        assert result is False

    def test_validate_image_invalid_format(self, adapter):
        """测试无效图片格式"""
        with patch('os.path.exists', return_value=True):
            result = asyncio.run(adapter._validate_image("test.txt"))
            assert result is False

    def test_validate_image_too_large(self, adapter):
        """测试图片文件过大"""
        with patch('os.path.exists', return_value=True):
            with patch('os.path.getsize', return_value=11 * 1024 * 1024):  # 11MB
                result = asyncio.run(adapter._validate_image("test.jpg"))
                assert result is False

    def test_preprocess_content(self, adapter):
        """测试内容预处理"""
        content = """
        <script>alert('test');</script>
        <p>正常内容</p>
        <a href="http://example.com">链接文本</a>
        <iframe src="http://example.com"></iframe>
        """
        
        result = asyncio.run(adapter._preprocess_content(content))
        
        # 检查script标签被移除
        assert "<script>" not in result
        assert "alert('test');" not in result
        
        # 检查iframe标签被移除
        assert "<iframe>" not in result
        
        # 检查链接被处理
        assert "链接文本" in result
        assert "href=" not in result
        
        # 检查正常内容保留
        assert "正常内容" in result


class TestCookieManager:
    """Cookie管理器测试类"""

    @pytest.fixture
    def cookie_manager(self, tmp_path):
        """创建Cookie管理器实例"""
        return CookieManager(str(tmp_path))

    def test_get_cookie_file_path(self, cookie_manager):
        """测试获取Cookie文件路径"""
        path = cookie_manager.get_cookie_file_path("toutiao", "test_account")
        assert "toutiao_test_account_cookies.json" in path

    def test_save_and_load_cookies(self, cookie_manager):
        """测试保存和加载Cookies"""
        test_data = {
            "cookies": [{"name": "test", "value": "value", "domain": "example.com"}],
            "origins": []
        }
        
        # 保存
        success = cookie_manager.save_cookies("toutiao", test_data, "test")
        assert success is True
        
        # 加载
        loaded_data = cookie_manager.load_cookies("toutiao", "test")
        assert loaded_data is not None
        assert loaded_data["cookies"] == test_data["cookies"]
        assert "_saved_at" in loaded_data

    def test_validate_cookies_format_valid(self, cookie_manager):
        """测试有效的Cookies格式验证"""
        valid_data = {
            "cookies": [{"name": "test", "value": "value", "domain": "example.com"}],
            "origins": []
        }
        
        result = cookie_manager.validate_cookies_format(valid_data)
        assert result is True

    def test_validate_cookies_format_invalid(self, cookie_manager):
        """测试无效的Cookies格式验证"""
        invalid_data = {"invalid": "data"}
        
        result = cookie_manager.validate_cookies_format(invalid_data)
        assert result is False

    def test_list_accounts(self, cookie_manager):
        """测试列出账户"""
        # 保存几个测试账户
        test_data = {"cookies": [], "origins": []}
        cookie_manager.save_cookies("toutiao", test_data, "account1")
        cookie_manager.save_cookies("toutiao", test_data, "account2")
        
        accounts = cookie_manager.list_accounts("toutiao")
        assert "account1" in accounts
        assert "account2" in accounts

    def test_delete_cookies(self, cookie_manager):
        """测试删除Cookies"""
        test_data = {"cookies": [], "origins": []}
        cookie_manager.save_cookies("toutiao", test_data, "test")
        
        # 确认文件存在
        loaded = cookie_manager.load_cookies("toutiao", "test")
        assert loaded is not None
        
        # 删除
        success = cookie_manager.delete_cookies("toutiao", "test")
        assert success is True
        
        # 确认文件已删除
        loaded = cookie_manager.load_cookies("toutiao", "test")
        assert loaded is None


class TestConfigManager:
    """配置管理器测试类"""

    def test_default_config(self):
        """测试默认配置"""
        manager = ConfigManager()
        config = manager.load_config()
        
        assert isinstance(config, ToutiaoPlaywrightConfig)
        assert config.browser.type == "chromium"
        assert config.browser.headless is False
        assert config.timeouts.page_timeout == 30000
        assert config.retry.max_retries == 3

    def test_config_validation_success(self):
        """测试配置验证成功"""
        manager = ConfigManager()
        config = manager.load_config()
        
        result = manager.validate_config(config)
        assert result is True

    def test_config_validation_failure(self):
        """测试配置验证失败"""
        manager = ConfigManager()
        config = ToutiaoPlaywrightConfig()
        config.browser.type = "invalid_browser"
        
        result = manager.validate_config(config)
        assert result is False


# 集成测试（需要实际的浏览器环境）
@pytest.mark.integration
class TestToutiaoPlaywrightIntegration:
    """集成测试类"""

    @pytest.mark.asyncio
    async def test_adapter_context_manager(self):
        """测试适配器上下文管理器"""
        adapter = ToutiaoPlaywrightAdapter(headless=True, timeout=5000)
        
        async with adapter:
            assert adapter._playwright is not None
            assert adapter._browser is not None
            assert adapter._context is not None
            assert adapter._page is not None
        
        # 退出后资源应该被清理
        assert adapter._playwright is None
        assert adapter._browser is None
        assert adapter._context is None
        assert adapter._page is None

    @pytest.mark.asyncio
    async def test_navigate_to_login_page(self):
        """测试导航到登录页面"""
        adapter = ToutiaoPlaywrightAdapter(headless=True, timeout=10000)
        
        async with adapter:
            await adapter._page.goto(adapter.login_url)
            
            # 检查页面标题或URL
            current_url = adapter._page.url
            assert "toutiao.com" in current_url


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
