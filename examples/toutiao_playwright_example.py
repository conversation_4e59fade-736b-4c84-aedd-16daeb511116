#!/usr/bin/env python3
"""
今日头条Playwright适配器使用示例

本示例展示如何使用ToutiaoPlaywrightAdapter发布文章到今日头条平台。
"""

import asyncio
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from src.textup.models import TransformedContent, Platform
from src.textup.utils.toutiao_login_helper import ToutiaoLoginHelper


async def login_example():
    """登录示例"""
    print("=== 今日头条登录示例 ===")
    
    # 创建登录助手
    login_helper = ToutiaoLoginHelper(
        headless=False,  # 显示浏览器窗口
        timeout=30000
    )
    
    # 方式1: 手动登录（推荐）
    print("开始手动登录流程...")
    success = await login_helper.login_and_save_cookies(
        account="my_account",  # 账户名称
        force_manual=True      # 强制手动登录
    )
    
    if success:
        print("登录成功！Cookies已保存")
    else:
        print("登录失败")
        return False
    
    # 验证cookies有效性
    print("验证cookies有效性...")
    is_valid = await login_helper.verify_cookies("my_account")
    
    return is_valid


async def publish_example():
    """发布文章示例"""
    print("=== 今日头条文章发布示例 ===")
    
    # 创建适配器实例
    adapter = ToutiaoPlaywrightAdapter(
        headless=False,  # 显示浏览器窗口，便于调试
        timeout=30000,   # 30秒超时
        max_retries=3    # 最大重试3次
    )
    
    # 准备认证凭证
    credentials = {
        "cookies_file": "cookies/toutiao_my_account_cookies.json"
    }
    
    # 准备文章内容
    content = TransformedContent(
        title="使用Playwright自动化发布文章到今日头条",
        content="""
        <h2>前言</h2>
        <p>今日头条是一个重要的内容发布平台，但其API不支持文章发布功能。本文介绍如何使用Playwright实现自动化发布。</p>
        
        <h2>主要特性</h2>
        <ul>
            <li>支持自动登录和Cookie管理</li>
            <li>智能重试机制</li>
            <li>完善的错误处理</li>
            <li>支持图片上传</li>
        </ul>
        
        <h2>使用方法</h2>
        <p>首先需要完成登录认证，然后就可以自动发布文章了。</p>
        
        <h2>总结</h2>
        <p>通过Playwright，我们可以轻松实现今日头条的自动化发布功能。</p>
        """,
        platform=Platform.TOUTIAO,
        content_format="html"
    )
    
    # 发布选项
    publish_options = {
        "tags": ["技术", "自动化", "Python"],
        "publish_time": datetime.now() + timedelta(minutes=5),  # 5分钟后发布
        "cover_image": "examples/cover.jpg"  # 封面图片路径（如果存在）
    }
    
    try:
        # 使用异步上下文管理器
        async with adapter:
            # 认证
            print("开始认证...")
            auth_result = await adapter.authenticate(credentials)
            
            if not auth_result.success:
                print(f"认证失败: {auth_result.error_message}")
                return False
            
            print("认证成功！")
            
            # 验证内容格式
            print("验证内容格式...")
            validation_result = await adapter.validate_format(content)
            
            if not validation_result.is_valid:
                print("内容格式验证失败:")
                for error in validation_result.errors:
                    print(f"  - {error.field}: {error.message}")
                return False
            
            print("内容格式验证通过！")
            
            # 发布文章
            print("开始发布文章...")
            publish_result = await adapter.publish(content, publish_options)
            
            if publish_result.success:
                print("文章发布成功！")
                print(f"文章ID: {publish_result.platform_post_id}")
                if publish_result.published_url:
                    print(f"文章链接: {publish_result.published_url}")
                return True
            else:
                print(f"文章发布失败: {publish_result.error_message}")
                if publish_result.error_details:
                    print(f"错误详情: {publish_result.error_details}")
                return False
                
    except Exception as e:
        print(f"发布过程中出现异常: {str(e)}")
        return False


async def batch_publish_example():
    """批量发布示例"""
    print("=== 批量发布示例 ===")
    
    # 准备多篇文章
    articles = [
        {
            "title": "Python自动化技巧分享（一）",
            "content": "<p>这是第一篇关于Python自动化的文章...</p>",
            "tags": ["Python", "自动化"]
        },
        {
            "title": "Python自动化技巧分享（二）",
            "content": "<p>这是第二篇关于Python自动化的文章...</p>",
            "tags": ["Python", "自动化", "进阶"]
        },
        {
            "title": "Python自动化技巧分享（三）",
            "content": "<p>这是第三篇关于Python自动化的文章...</p>",
            "tags": ["Python", "自动化", "实战"]
        }
    ]
    
    adapter = ToutiaoPlaywrightAdapter(headless=False)
    credentials = {"cookies_file": "cookies/toutiao_my_account_cookies.json"}
    
    success_count = 0
    
    try:
        async with adapter:
            # 认证一次即可
            auth_result = await adapter.authenticate(credentials)
            if not auth_result.success:
                print(f"认证失败: {auth_result.error_message}")
                return
            
            # 逐个发布文章
            for i, article_data in enumerate(articles, 1):
                print(f"\n发布第{i}篇文章: {article_data['title']}")
                
                content = TransformedContent(
                    title=article_data["title"],
                    content=article_data["content"],
                    platform=Platform.TOUTIAO,
                    content_format="html"
                )
                
                options = {
                    "tags": article_data["tags"],
                    # 每篇文章间隔10分钟发布
                    "publish_time": datetime.now() + timedelta(minutes=10 * i)
                }
                
                result = await adapter.publish(content, options)
                
                if result.success:
                    print(f"✓ 第{i}篇文章发布成功")
                    success_count += 1
                else:
                    print(f"✗ 第{i}篇文章发布失败: {result.error_message}")
                
                # 文章间隔时间，避免频率过高
                if i < len(articles):
                    print("等待30秒后发布下一篇...")
                    await asyncio.sleep(30)
    
    except Exception as e:
        print(f"批量发布过程中出现异常: {str(e)}")
    
    print(f"\n批量发布完成，成功发布 {success_count}/{len(articles)} 篇文章")


async def status_check_example():
    """状态检查示例"""
    print("=== 文章状态检查示例 ===")
    
    adapter = ToutiaoPlaywrightAdapter(headless=True)
    credentials = {"cookies_file": "cookies/toutiao_my_account_cookies.json"}
    
    try:
        async with adapter:
            auth_result = await adapter.authenticate(credentials)
            if not auth_result.success:
                print(f"认证失败: {auth_result.error_message}")
                return
            
            # 检查文章状态（需要文章ID）
            article_id = "*********"  # 替换为实际的文章ID
            status = await adapter.get_publish_status(article_id)
            
            print(f"文章状态: {status}")
            
    except Exception as e:
        print(f"状态检查失败: {str(e)}")


async def main():
    """主函数"""
    print("今日头条Playwright适配器示例")
    print("=" * 50)
    
    # 检查是否已有cookies
    cookies_dir = Path("cookies")
    cookies_dir.mkdir(exist_ok=True)
    
    cookies_file = cookies_dir / "toutiao_my_account_cookies.json"
    
    if not cookies_file.exists():
        print("未找到cookies文件，需要先登录")
        success = await login_example()
        if not success:
            print("登录失败，无法继续")
            return
    else:
        print("找到已保存的cookies文件")
    
    # 选择要运行的示例
    print("\n请选择要运行的示例:")
    print("1. 单篇文章发布")
    print("2. 批量文章发布")
    print("3. 文章状态检查")
    print("4. 重新登录")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        await publish_example()
    elif choice == "2":
        await batch_publish_example()
    elif choice == "3":
        await status_check_example()
    elif choice == "4":
        await login_example()
    else:
        print("无效选择")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
