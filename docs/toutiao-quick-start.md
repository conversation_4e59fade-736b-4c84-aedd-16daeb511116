# 今日头条快速上手指南

## 🚀 5分钟快速开始

### 1. 环境准备
```bash
# 安装依赖
uv pip install playwright
playwright install chromium
```

### 2. 账户登录
```bash
# 创建个人账户（推荐使用有意义的名称）
uv run python scripts/toutiao_cli.py login --account personal --manual
```

**操作步骤：**
1. 运行命令后浏览器自动打开
2. 在浏览器中完成今日头条登录
3. 登录成功后回到终端按 Enter
4. 系统自动保存 cookies

### 3. 发布文章
```bash
# 方式1：直接发布
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --title "我的第一篇文章" \
    --content "<p>这是文章内容</p>" \
    --tags "生活,分享"

# 方式2：使用文件发布
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --file my_article.json
```

### 4. 批量发布
```bash
# 创建批量文章文件 articles.json
uv run python scripts/toutiao_cli.py batch articles.json --account personal
```

## 📋 常用命令速查

### 账户管理
```bash
# 查看所有账户
uv run python scripts/toutiao_cli.py accounts --list

# 验证账户状态
uv run python scripts/toutiao_cli.py accounts --verify personal

# 删除账户
uv run python scripts/toutiao_cli.py accounts --delete old_account
```

### 文章发布
```bash
# 基本发布
uv run python scripts/toutiao_cli.py publish --account ACCOUNT --title "标题" --content "内容"

# 带标签发布
uv run python scripts/toutiao_cli.py publish --account ACCOUNT --title "标题" --content "内容" --tags "标签1,标签2"

# 文件发布
uv run python scripts/toutiao_cli.py publish --account ACCOUNT --file article.json

# 无头模式发布
uv run python scripts/toutiao_cli.py publish --account ACCOUNT --title "标题" --content "内容" --headless
```

### 批量操作
```bash
# 批量发布
uv run python scripts/toutiao_cli.py batch articles.json --account ACCOUNT

# 设置发布间隔
uv run python scripts/toutiao_cli.py batch articles.json --account ACCOUNT --interval 10

# 无头模式批量发布
uv run python scripts/toutiao_cli.py batch articles.json --account ACCOUNT --headless
```

## 📄 文件格式示例

### 单篇文章 JSON 格式
```json
{
  "title": "文章标题",
  "content": "<p>文章内容，支持HTML格式</p><h2>小标题</h2><p>更多内容...</p>",
  "tags": ["标签1", "标签2", "标签3"]
}
```

### 批量文章 JSON 格式
```json
[
  {
    "title": "第一篇文章",
    "content": "<p>第一篇文章的内容</p>",
    "tags": ["标签1", "标签2"]
  },
  {
    "title": "第二篇文章", 
    "content": "<p>第二篇文章的内容</p>",
    "tags": ["标签3", "标签4"]
  }
]
```

## ⚠️ 常见问题

### Q: 账户名称是什么？
A: 账户名称是您自定义的标识符，用于区分不同的今日头条账户。建议使用有意义的名称如 `personal`、`work` 等。

### Q: 登录失败怎么办？
A: 
1. 检查网络连接
2. 重新安装浏览器：`playwright install chromium --force`
3. 使用非无头模式调试：去掉 `--headless` 参数

### Q: Cookies 过期怎么办？
A: 重新登录即可：
```bash
uv run python scripts/toutiao_cli.py login --account personal --manual
```

### Q: 如何管理多个账户？
A: 为每个账户使用不同的账户名称：
```bash
uv run python scripts/toutiao_cli.py login --account personal --manual
uv run python scripts/toutiao_cli.py login --account work --manual
uv run python scripts/toutiao_cli.py accounts --list
```

## 🔗 更多资源

- **详细文档**: [今日头条账户管理和Cookie机制详解](toutiao-account-management.md)
- **开发测试**: [开发测试指南](testing/development-testing-guide.md)
- **故障排除**: 参考详细文档中的故障排除章节

---

**提示**: 首次使用建议先用测试账户熟悉流程，确认无误后再使用正式账户。
