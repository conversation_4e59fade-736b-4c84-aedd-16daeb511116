# 今日头条Playwright适配器

## 概述

今日头条Playwright适配器是一个基于Playwright的自动化工具，用于发布文章到今日头条平台。由于今日头条API不支持文章发布功能，本适配器通过模拟浏览器操作来实现自动化发布。

## 主要特性

- ✅ **自动登录认证**: 支持手动登录和Cookie管理
- ✅ **智能重试机制**: 内置多层重试策略，提高发布成功率
- ✅ **完善错误处理**: 详细的错误分类和处理机制
- ✅ **图片上传支持**: 支持封面图片和内容图片上传
- ✅ **定时发布**: 支持设置文章发布时间
- ✅ **批量发布**: 支持批量发布多篇文章
- ✅ **配置管理**: 灵活的配置文件管理
- ✅ **反检测机制**: 内置反自动化检测功能

## 安装要求

### 系统要求
- Python 3.8+
- 支持的操作系统: Windows, macOS, Linux

### 依赖包
```bash
pip install playwright>=1.40.0
pip install pyyaml>=6.0.1
```

### 安装浏览器
```bash
playwright install chromium
```

## 快速开始

### 1. 基本使用

```python
import asyncio
from src.textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from src.textup.models import TransformedContent, Platform

async def main():
    # 创建适配器实例
    adapter = ToutiaoPlaywrightAdapter(
        headless=False,  # 显示浏览器窗口
        timeout=30000    # 30秒超时
    )
    
    # 准备认证凭证
    credentials = {
        "cookies_file": "cookies/toutiao_my_account_cookies.json"
    }
    
    # 准备文章内容
    content = TransformedContent(
        title="我的第一篇文章",
        content="<p>这是文章内容...</p>",
        platform=Platform.TOUTIAO,
        content_format="html"
    )
    
    # 发布选项
    options = {
        "tags": ["技术", "分享"],
        "cover_image": "path/to/cover.jpg"
    }
    
    async with adapter:
        # 认证
        auth_result = await adapter.authenticate(credentials)
        if not auth_result.success:
            print(f"认证失败: {auth_result.error_message}")
            return
        
        # 发布文章
        result = await adapter.publish(content, options)
        if result.success:
            print("发布成功！")
            print(f"文章ID: {result.platform_post_id}")
        else:
            print(f"发布失败: {result.error_message}")

asyncio.run(main())
```

### 2. 登录认证

首次使用需要完成登录认证：

```python
from src.textup.utils.toutiao_login_helper import ToutiaoLoginHelper

async def login():
    helper = ToutiaoLoginHelper(headless=False)
    
    # 手动登录（推荐）
    success = await helper.login_and_save_cookies(
        account="my_account",
        force_manual=True
    )
    
    if success:
        print("登录成功，cookies已保存")
    
    return success

# 运行登录
asyncio.run(login())
```

### 3. 配置文件使用

创建配置文件 `config/toutiao.yaml`:

```yaml
browser:
  headless: false
  type: "chromium"

timeouts:
  page_timeout: 30000
  upload_timeout: 60000

retry:
  max_retries: 3
  delay_sequence: [1, 2, 4]

content:
  max_title_length: 100
  max_content_length: 50000
```

使用配置文件：

```python
from src.textup.utils.config_manager import load_config

# 加载配置
config = load_config("config/toutiao.yaml")

# 使用配置创建适配器
adapter = ToutiaoPlaywrightAdapter(
    headless=config.browser.headless,
    timeout=config.timeouts.page_timeout,
    max_retries=config.retry.max_retries
)
```

## 高级功能

### 批量发布

```python
async def batch_publish():
    articles = [
        {"title": "文章1", "content": "内容1", "tags": ["tag1"]},
        {"title": "文章2", "content": "内容2", "tags": ["tag2"]},
    ]
    
    adapter = ToutiaoPlaywrightAdapter()
    
    async with adapter:
        await adapter.authenticate(credentials)
        
        for article in articles:
            content = TransformedContent(
                title=article["title"],
                content=article["content"],
                platform=Platform.TOUTIAO
            )
            
            result = await adapter.publish(content, {"tags": article["tags"]})
            print(f"文章 '{article['title']}' 发布结果: {result.success}")
            
            # 间隔30秒
            await asyncio.sleep(30)
```

### 定时发布

```python
from datetime import datetime, timedelta

# 设置1小时后发布
publish_time = datetime.now() + timedelta(hours=1)

options = {
    "publish_time": publish_time,
    "tags": ["定时发布"]
}

result = await adapter.publish(content, options)
```

### 图片处理

```python
# 设置封面图片
options = {
    "cover_image": "path/to/cover.jpg"
}

# 内容中包含图片
content_with_images = """
<p>这是一段文字</p>
<img src="path/to/image1.jpg" alt="图片1">
<p>更多内容</p>
<img src="path/to/image2.png" alt="图片2">
"""

content = TransformedContent(
    title="包含图片的文章",
    content=content_with_images,
    platform=Platform.TOUTIAO
)
```

## 错误处理

适配器内置了完善的错误处理机制：

### 常见错误类型

1. **网络错误**: 自动重试
2. **认证错误**: 需要重新登录
3. **内容错误**: 检查内容格式
4. **权限错误**: 检查账户权限

### 错误处理示例

```python
try:
    result = await adapter.publish(content, options)
    if not result.success:
        if "认证" in result.error_message:
            print("需要重新登录")
            # 重新认证逻辑
        elif "内容" in result.error_message:
            print("内容格式有问题")
            # 内容检查逻辑
        else:
            print(f"其他错误: {result.error_message}")
except Exception as e:
    print(f"发布异常: {str(e)}")
```

## 配置选项

### 浏览器配置
- `browser.type`: 浏览器类型 (chromium/firefox/webkit)
- `browser.headless`: 是否无头模式
- `browser.executable_path`: 浏览器可执行文件路径

### 超时配置
- `timeouts.page_timeout`: 页面操作超时时间
- `timeouts.upload_timeout`: 上传超时时间
- `timeouts.login_timeout`: 登录超时时间

### 重试配置
- `retry.max_retries`: 最大重试次数
- `retry.delay_sequence`: 重试延迟序列
- `retry.max_publish_retries`: 发布最大重试次数

### 内容配置
- `content.max_title_length`: 标题最大长度
- `content.max_content_length`: 内容最大长度
- `content.supported_image_formats`: 支持的图片格式
- `content.max_image_size_mb`: 图片最大大小

## 最佳实践

### 1. 登录管理
- 使用手动登录确保安全性
- 定期验证cookies有效性
- 为不同账户使用不同的cookies文件

### 2. 发布策略
- 控制发布频率，避免被限制
- 使用定时发布分散发布时间
- 批量发布时增加间隔时间

### 3. 内容优化
- 确保标题和内容符合平台要求
- 优化图片大小和格式
- 合理使用标签

### 4. 错误处理
- 启用详细日志记录
- 保存错误时的截图
- 实现自动重试机制

## 故障排除

### 常见问题

**Q: 登录失败怎么办？**
A: 
1. 检查网络连接
2. 清除旧的cookies文件
3. 使用手动登录模式
4. 检查账户是否被限制

**Q: 发布失败怎么办？**
A:
1. 检查内容格式是否正确
2. 验证图片文件是否存在
3. 确认账户权限
4. 查看详细错误信息

**Q: 页面元素找不到怎么办？**
A:
1. 今日头条页面可能有更新
2. 更新选择器配置
3. 检查页面是否完全加载
4. 增加等待时间

### 调试模式

启用调试模式获取更多信息：

```python
adapter = ToutiaoPlaywrightAdapter(
    headless=False,  # 显示浏览器
    timeout=60000    # 增加超时时间
)

# 在配置文件中启用调试
debug:
  enabled: true
  pause_on_steps: true
  verbose_logging: true
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的文章发布功能
- 实现登录认证和Cookie管理
- 添加重试机制和错误处理

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。

## 免责声明

本工具仅供学习和研究使用，使用者需要遵守今日头条的服务条款和相关法律法规。作者不对使用本工具产生的任何后果承担责任。
