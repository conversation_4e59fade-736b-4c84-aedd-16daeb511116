# TextUp 测试文档中心 🧪

本目录包含 TextUp 项目的测试相关文档，为开发者、测试人员和贡献者提供完整的测试指导。

## 📋 目录

- [测试策略和方法](#测试策略和方法)
- [测试指南](#测试指南)
- [测试报告](#测试报告)
- [快速开始](#快速开始)
- [测试工具和脚本](#测试工具和脚本)
- [相关资源](#相关资源)

---

## 🎯 测试策略和方法

### 测试层次结构

TextUp 项目采用多层次的测试策略：

| 测试类型 | 目录位置 | 覆盖范围 | 执行频率 |
|----------|----------|----------|----------|
| **单元测试** | `tests/unit/` | 单个函数/类 | 每次提交 |
| **集成测试** | `tests/integration/` | 模块间交互 | 每日构建 |
| **端到端测试** | `tests/e2e/` | 完整工作流 | 发布前 |
| **手动测试** | 文档指导 | 用户场景 | 重要功能 |

### 测试原则

- ✅ **快速反馈** - 单元测试在30秒内完成
- ✅ **可靠性** - 测试结果稳定可重复
- ✅ **覆盖率** - 核心功能覆盖率 > 80%
- ✅ **真实性** - 测试环境接近生产环境

---

## 📚 测试指南

### 开发测试指南

| 文档 | 适用人群 | 预计用时 | 描述 |
|------|----------|----------|------|
| [开发测试指南](development-testing-guide.md) | 开发者 | 30-60分钟 | 完整的开发环境测试流程 |
| [开发测试改进报告](DEVELOPMENT_TESTING_GUIDE_IMPROVEMENTS.md) | 维护者 | 10分钟 | 文档改进历史和最佳实践 |

### 平台特定测试指南

| 文档 | 平台 | 类型 | 描述 |
|------|------|------|------|
| [知乎CLI测试指南](zhihu-cli-test-guide.md) | 知乎 | 命令行测试 | 知乎平台命令行测试详细指南 |
| [知乎测试完整指南](readme-zhihu-test.md) | 知乎 | 综合测试 | 知乎平台完整测试流程 |

### 自动化测试指南

| 文档 | 技术栈 | 描述 |
|------|--------|------|
| [自动化工作流测试](textup-auto-workflow-test.md) | GitHub Actions | 自动化测试工作流配置 |
| [通用AI工作流生成器](universal-ai-workflow-generator.md) | AI辅助 | AI驱动的测试工作流生成 |

---

## 📊 测试报告

### 平台测试报告

| 报告 | 平台 | 最后更新 | 状态 |
|------|------|----------|------|
| [知乎测试报告](zhihu-test-report.md) | 知乎 | 最新 | ✅ 通过 |

### 改进报告

| 报告 | 类型 | 描述 |
|------|------|------|
| [测试改进总结](testing-improvements-summary.md) | 改进记录 | 测试流程和工具的改进历史 |

---

## 🚀 快速开始

### 环境检查

```bash
# 快速环境检查（30秒）
./dev-scripts/quick-test.sh

# 完整环境诊断（2-3分钟）
./dev-scripts/troubleshoot.sh
```

### 单元测试

```bash
# 运行所有单元测试
pytest tests/unit/ -v

# 运行特定模块测试
pytest tests/unit/test_cli.py -v

# 生成覆盖率报告
pytest tests/unit/ --cov=src/textup --cov-report=html
```

### 集成测试

```bash
# 运行集成测试
pytest tests/integration/ -v

# 知乎平台集成测试
pytest tests/integration/zhihu/ -v
```

### 手动测试

```bash
# 知乎平台预览测试
textup publish tests/data/test-zhihu-article.md --platform zhihu --dry-run

# 微博平台预览测试
textup publish tests/data/test-article.md --platform weibo --dry-run

# 多平台批量测试
textup publish tests/test-content/ --platforms weibo,zhihu --dry-run
```

---

## 🛠️ 测试工具和脚本

### 开发脚本

| 脚本 | 功能 | 位置 |
|------|------|------|
| `quick-test.sh` | 快速基础测试 | `dev-scripts/` |
| `test-all-platforms.sh` | 全平台功能测试 | `dev-scripts/` |
| `troubleshoot.sh` | 环境诊断 | `dev-scripts/` |
| `analyze-logs.sh` | 日志分析 | `dev-scripts/` |

### 测试数据

| 数据类型 | 位置 | 描述 |
|----------|------|------|
| 测试文章 | `tests/data/` | 各平台测试用的文章内容 |
| 配置文件 | `tests/test-config.yaml` | 测试环境配置 |
| 测试结果 | `tests/test-results/` | 测试执行结果和报告 |

### CI/CD 集成

```yaml
# GitHub Actions 示例
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          ./dev-scripts/quick-test.sh
          pytest tests/ -v --cov=src/textup
```

---

## 📖 测试最佳实践

### 编写测试

1. **命名规范**
   ```python
   def test_should_publish_to_weibo_when_valid_content():
       # Given - 准备测试数据
       # When - 执行被测试的操作
       # Then - 验证结果
   ```

2. **测试结构**
   ```
   tests/
   ├── unit/           # 单元测试
   │   ├── test_cli.py
   │   └── test_models.py
   ├── integration/    # 集成测试
   │   └── test_workflows.py
   └── fixtures/       # 测试数据
       └── sample_articles.py
   ```

3. **Mock 使用**
   ```python
   @patch('textup.adapters.weibo.WeiboAdapter.publish')
   def test_publish_success(mock_publish):
       mock_publish.return_value = PublishResult(success=True)
       # 测试逻辑
   ```

### 调试测试

```bash
# 运行单个测试并显示详细输出
pytest tests/unit/test_cli.py::test_version_command -v -s

# 进入调试模式
pytest tests/unit/test_cli.py::test_version_command --pdb

# 只运行失败的测试
pytest --lf
```

---

## 🔗 相关资源

### 项目文档

- **[用户文档](../usage/)** - 用户使用指南
- **[API 文档](../api/)** - 开发者API参考
- **[项目架构](../project-structure.md)** - 项目结构说明

### 外部资源

- **[pytest 文档](https://docs.pytest.org/)** - Python测试框架
- **[coverage.py](https://coverage.readthedocs.io/)** - 代码覆盖率工具
- **[GitHub Actions](https://docs.github.com/en/actions)** - CI/CD 工作流

### 测试工具

- **pytest** - 主要测试框架
- **pytest-cov** - 覆盖率插件
- **pytest-mock** - Mock 支持
- **playwright** - 端到端测试（知乎平台）

---

## 📞 获取帮助

### 测试问题排查

1. **运行诊断脚本**
   ```bash
   ./dev-scripts/troubleshoot.sh
   ```

2. **查看测试日志**
   ```bash
   pytest tests/ -v --tb=short
   ```

3. **检查覆盖率报告**
   ```bash
   pytest --cov=src/textup --cov-report=html
   open htmlcov/index.html
   ```

### 贡献测试

1. 阅读 [开发测试指南](development-testing-guide.md)
2. 运行现有测试确保环境正常
3. 编写新的测试用例
4. 提交 Pull Request

---

**测试是保证代码质量的重要手段，让我们一起构建更可靠的 TextUp！** 🚀

---

**最后更新**：2025-09-04
**文档版本**：v2.0
**维护团队**：TextUp 开发团队
