# 知乎平台命令行测试完整指南

## 🎯 测试目的

本文档提供了使用 TextUp 工具通过命令行测试知乎平台文章发布功能的完整指南。TextUp 使用 Playwright 自动化技术实现知乎文章发布，无需依赖官方 API。

## 📋 快速测试清单

### ✅ 环境准备检查
- [ ] Python 3.9+ 已安装
- [ ] 项目依赖已安装
- [ ] Playwright 浏览器已安装
- [ ] 知乎账号准备就绪

### ✅ 功能测试检查
- [ ] 预览模式测试通过
- [ ] 内容解析正常
- [ ] 浏览器自动化工作
- [ ] 实际发布成功

## 🚀 一键快速测试

### 方法1: 使用现有测试脚本

```bash
# 进入项目目录
cd /path/to/textup

# 使用快速测试脚本
./dev-scripts/quick-test.sh

# 或使用自定义测试脚本
./test-zhihu-publish.sh --preview-only
```

### 方法2: 使用启动脚本

```bash
# 运行一键启动脚本
./start-textup.sh

# 在菜单中选择测试选项
```

## 🔧 环境设置步骤

### Step 1: 环境检查

```bash
# 检查 Python 版本（需要 3.9+）
python3 --version

# 检查是否有 uv 包管理器
uv --version || curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Step 2: 项目安装

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境 (macOS/Linux)
source .venv/bin/activate

# 激活虚拟环境 (Windows)
# .venv\Scripts\activate

# 安装项目依赖
uv pip install -e .

# 安装 Playwright 浏览器
playwright install chromium

# 验证安装
textup --version
```

### Step 3: 基础配置

```bash
# 初始化配置
textup config --interactive

# 设置知乎平台配置
textup config --set platforms.zhihu.enabled=true
textup config --set platforms.zhihu.headless=false  # 测试时建议可见模式
textup config --set platforms.zhihu.timeout=30000

# 查看配置
textup config --list
```

## 📝 测试文章准备

创建测试文件 `test-zhihu-article.md`：

```markdown
---
title: "TextUp 知乎发布功能测试"
tags: ["测试", "自动化", "工具"]
category: "技术分享"
---

# TextUp 知乎发布功能测试

这是一篇测试 TextUp 知乎发布功能的文章。

## 功能测试点

### 格式支持测试
- **粗体文本**
- *斜体文本*
- `行内代码`
- [链接测试](https://github.com)

### 代码块测试
```python
def test_textup():
    print("TextUp 知乎发布测试")
    return "success"
```

### 列表测试
1. 有序列表项 1
2. 有序列表项 2

- 无序列表项 1
- 无序列表项 2

## 测试结论

如果您在知乎上看到这篇文章，说明 TextUp 发布功能工作正常！

---
测试时间：2024-01-15
```

## 🧪 执行测试命令

### 1. 预览模式测试（推荐）

```bash
# 基础预览测试
textup publish test-zhihu-article.md --platform zhihu --dry-run

# 详细预览测试
textup --debug publish test-zhihu-article.md --platform zhihu --dry-run
```

**预期输出**：
```
[INFO] 📝 准备发布内容...
[INFO] 📄 找到 1 个文件
[INFO] ✅ 解析成功: TextUp 知乎发布功能测试
[INFO] 📝 预览模式，显示转换后的内容...
[INFO] 🎯 目标平台: 知乎
```

### 2. 实际发布测试

**⚠️ 警告：这将真实发布到知乎平台！**

```bash
# 基础发布测试
textup publish test-zhihu-article.md --platform zhihu

# 带调试信息的发布测试
textup --debug publish test-zhihu-article.md --platform zhihu

# 交互式发布测试
textup publish --interactive
```

### 3. 批量测试

```bash
# 创建测试文章目录
mkdir test-articles
cp test-zhihu-article.md test-articles/

# 批量预览测试
textup publish test-articles/ --recursive --platform zhihu --dry-run

# 批量发布测试（谨慎使用）
textup publish test-articles/ --recursive --platform zhihu
```

## 📊 测试验证

### 成功标志

发布成功时的输出示例：
```
[INFO] ✅ 发布成功: TextUp 知乎发布功能测试
[INFO] 🔗 文章链接: https://zhuanlan.zhihu.com/p/xxxxxxxx
[INFO] ⏰ 发布时间: 2024-01-15 14:30:25
[INFO] 📊 发布统计: 成功=1, 失败=0
```

### 验证步骤

1. **检查知乎平台**：
   - 登录知乎账号
   - 查看"创作中心" → "内容管理"
   - 确认文章已发布或在草稿中

2. **验证内容格式**：
   - 检查标题是否正确
   - 验证 Markdown 格式转换
   - 确认标签和分类设置

3. **测试文章访问**：
   - 通过返回的链接访问文章
   - 确认内容显示正常

## 🔍 故障排除

### 常见问题及解决方案

#### 问题 1: Playwright 浏览器未安装

```bash
# 错误信息
playwright._impl._api_types.Error: Executable doesn't exist

# 解决方案
playwright install chromium
# 或安装所有浏览器
playwright install
```

#### 问题 2: 知乎登录失败

```bash
# 设置非无头模式观察登录过程
textup config --set platforms.zhihu.headless=false

# 增加操作延迟
textup config --set platforms.zhihu.action_delay=2000

# 使用调试模式
textup --debug publish test-zhihu-article.md --platform zhihu
```

#### 问题 3: 网络超时

```bash
# 增加超时时间
textup config --set platforms.zhihu.timeout=60000

# 重试发布
textup publish test-zhihu-article.md --platform zhihu --retry 3
```

#### 问题 4: 内容解析错误

```bash
# 验证文章格式
textup validate test-zhihu-article.md

# 检查 frontmatter 语法
head -10 test-zhihu-article.md
```

### 调试命令

```bash
# 查看详细日志
tail -f ~/.textup/logs/textup.log

# 生成诊断报告
textup diagnose --platform zhihu

# 重置配置
textup config --reset

# 清理缓存
rm -rf ~/.textup/cache/
```

## 📈 高级测试选项

### 性能测试

```bash
# 测试发布时间
time textup publish test-zhihu-article.md --platform zhihu --dry-run

# 并发测试（谨慎使用）
for i in {1..3}; do
  cp test-zhihu-article.md "test-article-$i.md"
done
textup publish test-article-*.md --platform zhihu --dry-run
```

### 自定义配置测试

```bash
# 测试不同浏览器配置
textup config --set platforms.zhihu.browser=firefox
textup config --set platforms.zhihu.viewport_width=1920
textup config --set platforms.zhihu.viewport_height=1080

# 测试代理设置
textup config --set platforms.zhihu.proxy_server="http://proxy:8080"
```

### 错误处理测试

```bash
# 测试无效文件处理
textup publish nonexistent.md --platform zhihu --dry-run

# 测试网络异常处理
# (需要临时断开网络)
textup publish test-zhihu-article.md --platform zhihu --dry-run
```

## 🎯 测试最佳实践

### 1. 测试环境隔离
- 使用专门的测试知乎账号
- 在独立的虚拟环境中测试
- 定期清理测试数据

### 2. 渐进式测试
1. 先执行预览模式测试
2. 再进行单篇文章发布测试
3. 最后尝试批量发布测试

### 3. 安全考虑
- 不要在生产账号上进行测试
- 测试文章使用明显的测试标识
- 及时清理测试发布的文章

### 4. 监控和日志
```bash
# 启用详细日志
export TEXTUP_LOG_LEVEL=DEBUG

# 监控系统资源
top -p $(pgrep -f textup)

# 检查网络连接
ping zhihu.com
```

## 📋 完整测试检查清单

### 环境准备
- [ ] Python 3.9+ 环境
- [ ] uv 包管理器安装
- [ ] 虚拟环境创建和激活
- [ ] 项目依赖安装完成
- [ ] Playwright 浏览器安装
- [ ] TextUp 工具可用

### 配置检查
- [ ] 基础配置初始化
- [ ] 知乎平台配置启用
- [ ] 浏览器参数配置合理
- [ ] 日志级别设置适当

### 功能测试
- [ ] 帮助命令正常显示
- [ ] 版本信息正确显示
- [ ] 配置命令工作正常
- [ ] 文章解析功能正常
- [ ] 预览模式工作正常

### 发布测试
- [ ] 浏览器自动启动成功
- [ ] 知乎网站访问正常
- [ ] 文章内容填写正确
- [ ] 发布操作执行成功
- [ ] 返回结果信息完整

### 验证测试
- [ ] 知乎平台可见发布内容
- [ ] 文章格式转换正确
- [ ] 标题和标签设置正确
- [ ] 文章链接可以访问

## 🚀 快速测试命令汇总

```bash
# 1. 环境准备
python3 --version && uv --version
uv venv && source .venv/bin/activate
uv pip install -e . && playwright install chromium

# 2. 配置初始化
textup config --interactive
textup config --set platforms.zhihu.enabled=true
textup config --set platforms.zhihu.headless=false

# 3. 创建测试文章
cat > test-zhihu.md << 'EOF'
---
title: "测试文章"
tags: ["测试"]
---
# 测试文章
这是一篇测试文章。
EOF

# 4. 执行测试
textup --version
textup publish test-zhihu.md --platform zhihu --dry-run
textup publish test-zhihu.md --platform zhihu

# 5. 验证结果
textup status
textup history
```

## 📞 获取帮助

### 官方文档
- [快速开始指南](docs/quick-start-guide.md)
- [本地测试指南](docs/local-testing-guide.md)
- [知乎适配器文档](docs/adapters/zhihu_playwright.md)

### 命令行帮助
```bash
textup --help                    # 主帮助
textup publish --help            # 发布帮助
textup config --help             # 配置帮助
textup auth --help               # 认证帮助
```

### 社区支持
- GitHub Issues: 报告问题和获取帮助
- 文档站点: 查看详细文档
- 示例代码: 参考实际使用案例

---

## 🎉 测试成功！

如果您成功完成了所有测试步骤，恭喜您已经掌握了使用 TextUp 进行知乎平台文章发布的完整流程！

现在您可以：
- 使用 TextUp 高效发布知乎文章
- 批量处理多篇文章发布
- 自动化您的内容发布工作流
- 扩展到其他支持的平台

**Happy Publishing! 🚀📝**