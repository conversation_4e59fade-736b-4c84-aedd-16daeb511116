# 知乎平台命令行测试指南

## 📝 概述

本指南将详细介绍如何使用TextUp工具通过命令行测试上传文章到知乎平台。TextUp使用Playwright自动化技术实现知乎平台的文章发布，无需依赖官方API。

## 🎯 测试目标

- ✅ 验证TextUp工具的知乎发布功能
- ✅ 测试Markdown文章的格式转换
- ✅ 验证自动化登录和发布流程
- ✅ 测试错误处理和恢复机制

## 📋 前置要求

### 系统要求
- **操作系统**: macOS、Linux 或 Windows
- **Python版本**: 3.9+ 
- **内存**: 至少2GB可用内存
- **网络**: 稳定的互联网连接

### 账号要求
- 有效的知乎账号
- 账号状态正常（未被限制发布）
- 建议使用测试专用账号

## 🚀 快速开始

### 1. 一键测试脚本

我们提供了一键测试脚本，可以快速验证所有功能：

```bash
# 进入项目目录
cd /path/to/textup

# 运行测试脚本（仅预览，不实际发布）
./test-zhihu-publish.sh --preview-only

# 运行完整测试（包括实际发布）
./test-zhihu-publish.sh
```

### 2. 手动测试步骤

如果您希望逐步了解整个过程，可以按以下步骤手动执行：

## 📦 环境设置

### Step 1: 检查Python环境

```bash
# 检查Python版本
python3 --version

# 应该显示 3.9.0 或更高版本
```

### Step 2: 安装uv包管理器

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows PowerShell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 验证安装
uv --version
```

### Step 3: 创建虚拟环境

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
# macOS/Linux:
source .venv/bin/activate

# Windows:
.venv\Scripts\activate
```

### Step 4: 安装TextUp

```bash
# 安装项目
uv pip install -e .

# 安装Playwright浏览器
playwright install chromium

# 验证安装
textup --version
```

## ⚙️ 配置设置

### 初始化配置

```bash
# 初始化基础配置
textup config --interactive
```

按提示输入：
- **应用名称**: "TextUp测试"
- **日志级别**: "INFO"
- **其他设置**: 使用默认值

### 知乎平台配置

由于知乎使用Playwright自动化，主要需要配置浏览器相关选项：

```bash
# 设置知乎平台基础配置
textup config --set platforms.zhihu.enabled=true
textup config --set platforms.zhihu.headless=false  # 测试时建议设为false以观察过程
textup config --set platforms.zhihu.timeout=30000   # 30秒超时
```

### 查看当前配置

```bash
# 查看所有配置
textup config --list

# 查看知乎平台配置
textup config --get platforms.zhihu
```

## 📝 准备测试内容

### 创建测试文章

创建一个Markdown文件 `test-article.md`：

```markdown
---
title: "TextUp知乎发布测试"
tags: ["测试", "工具", "自动化"]
category: "技术分享"
---

# TextUp知乎发布测试

这是一篇用于测试TextUp知乎发布功能的文章。

## 测试内容

- **文本格式**: 支持**粗体**、*斜体*
- **链接**: [TextUp项目](https://github.com/textup)
- **代码**: `console.log('Hello TextUp')`

## 代码块测试

```python
def test_textup():
    print("TextUp知乎发布测试")
    return True
```

## 列表测试

1. 有序列表项1
2. 有序列表项2

- 无序列表项1
- 无序列表项2

## 结论

如果您看到这篇文章，说明TextUp知乎发布功能正常工作！

---
测试时间: 2024-01-15
```

## 🧪 执行测试

### 1. 预览测试（推荐先执行）

```bash
# 预览模式 - 不会实际发布，只验证内容解析和格式转换
textup publish test-article.md --platform zhihu --dry-run
```

预期输出：
```
[INFO] 📝 准备发布内容...
[INFO] 📄 找到 1 个文件
[INFO] ✅ 解析成功: TextUp知乎发布测试
[INFO] 📝 预览模式，显示转换后的内容...
[INFO] 🎯 目标平台: 知乎
[INFO] 📊 内容统计: 标题长度=12, 正文长度=245
```

### 2. 实际发布测试

**⚠️ 注意：这会真实发布到知乎平台！**

```bash
# 实际发布到知乎
textup publish test-article.md --platform zhihu
```

发布过程中会：
1. 启动Chrome浏览器（如果设置了headless=false可以看到）
2. 自动导航到知乎创作页面
3. 自动填写文章内容
4. 提交发布

### 3. 批量测试

```bash
# 批量发布目录中的所有.md文件
textup publish ./test-articles/ --recursive --platform zhihu --dry-run
```

## 📊 测试验证

### 成功标志

发布成功时会看到：
```
[INFO] ✅ 发布成功: TextUp知乎发布测试
[INFO] 🔗 文章链接: https://zhuanlan.zhihu.com/p/xxxxxxxx
[INFO] ⏰ 发布时间: 2024-01-15 14:30:25
[INFO] 📊 发布统计: 成功=1, 失败=0
```

### 验证步骤

1. **检查知乎后台**：登录知乎，检查草稿箱或已发布文章
2. **验证内容格式**：确认Markdown格式正确转换
3. **检查元数据**：验证标题、标签、分类是否正确设置

## 🔧 高级测试选项

### 调试模式

```bash
# 启用详细日志
textup --debug publish test-article.md --platform zhihu
```

### 自定义浏览器配置

```bash
# 设置自定义User-Agent
textup config --set platforms.zhihu.user_agent="Mozilla/5.0 (Custom TextUp)"

# 设置窗口大小
textup config --set platforms.zhihu.viewport_width=1920
textup config --set platforms.zhihu.viewport_height=1080

# 启用/禁用图片加载
textup config --set platforms.zhihu.load_images=false
```

### 时间控制

```bash
# 设置操作间隔（毫秒）
textup config --set platforms.zhihu.action_delay=1000

# 设置页面加载超时
textup config --set platforms.zhihu.page_timeout=60000
```

## ❗ 常见问题与解决

### 问题1: 浏览器启动失败

**错误信息**:
```
playwright._impl._api_types.Error: Executable doesn't exist at /path/to/chromium
```

**解决方案**:
```bash
# 重新安装Playwright浏览器
playwright install chromium

# 或者安装所有浏览器
playwright install
```

### 问题2: 登录失败或超时

**可能原因**:
- 网络连接问题
- 知乎检测到自动化行为
- 账号被限制

**解决方案**:
```bash
# 1. 设置为非无头模式，手动观察
textup config --set platforms.zhihu.headless=false

# 2. 增加操作延迟
textup config --set platforms.zhihu.action_delay=2000

# 3. 使用调试模式
textup --debug publish test-article.md --platform zhihu
```

### 问题3: 内容格式错误

**检查步骤**:
1. 使用预览模式检查转换结果
2. 验证Markdown语法正确性
3. 检查frontmatter格式

```bash
# 验证内容解析
textup validate test-article.md

# 预览转换结果
textup preview test-article.md --platform zhihu
```

### 问题4: 发布后找不到文章

**检查位置**:
1. 知乎个人主页的"文章"部分
2. 专栏管理页面
3. 草稿箱（可能未正式发布）

## 📋 测试检查清单

### 发布前检查
- [ ] Python环境 >= 3.9
- [ ] TextUp已正确安装
- [ ] Playwright浏览器已安装
- [ ] 测试文章格式正确
- [ ] 网络连接稳定

### 功能测试
- [ ] 预览模式工作正常
- [ ] 内容解析无错误
- [ ] 格式转换正确
- [ ] 浏览器能正常启动
- [ ] 能访问知乎网站

### 发布测试
- [ ] 自动登录成功（如需要）
- [ ] 内容填写正确
- [ ] 标题和标签设置正确
- [ ] 发布操作成功执行
- [ ] 返回正确的文章链接

### 后续验证
- [ ] 文章在知乎平台可见
- [ ] 格式显示正常
- [ ] 链接和代码块正确渲染
- [ ] 标签和分类正确设置

## 🛠️ 故障排除工具

### 日志查看

```bash
# 查看详细日志
tail -f ~/.textup/logs/textup.log

# 查看特定时间的日志
grep "2024-01-15" ~/.textup/logs/textup.log
```

### 配置重置

```bash
# 备份当前配置
textup config --backup backup-$(date +%Y%m%d).yaml

# 重置配置
textup config --reset

# 恢复配置
textup config --restore backup-20240115.yaml
```

### 缓存清理

```bash
# 清理浏览器缓存
rm -rf ~/.textup/cache/browser/

# 清理会话数据
rm -rf ~/.textup/cache/sessions/
```

## 📞 获取帮助

### 命令行帮助

```bash
# 主命令帮助
textup --help

# 发布命令详细帮助
textup publish --help

# 配置命令帮助
textup config --help
```

### 诊断信息

```bash
# 生成诊断报告
textup diagnose --platform zhihu --output diagnosis.txt

# 查看系统信息
textup info --system
```

## 🎉 测试完成

完成所有测试步骤后，您应该能够：

1. ✅ 成功使用命令行发布文章到知乎
2. ✅ 理解TextUp的基本配置和使用方法
3. ✅ 掌握常见问题的解决方法
4. ✅ 能够进行批量内容发布

## 📚 相关文档

- [TextUp快速上手指南](docs/quick-start-guide.md)
- [本地测试指南](docs/local-testing-guide.md)
- [知乎Playwright适配器文档](docs/adapters/zhihu_playwright.md)
- [API参考文档](docs/api/)

---

**祝测试愉快！** 🚀

如有问题，请查看相关文档或提交Issue反馈。