#!/usr/bin/env python3
"""
今日头条适配器集成测试
测试实际的文档上传功能
"""

import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao import ToutiaoAdapter
from textup.models import Content, ContentFormat, TransformedContent


class ToutiaoUploadIntegrationTest:
    """今日头条上传集成测试"""
    
    def __init__(self):
        self.adapter = ToutiaoAdapter()
        self.results = []
        
    def log(self, message, success=True):
        """记录测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {message}")
        self.results.append({
            "message": message,
            "success": success,
            "timestamp": datetime.now().isoformat()
        })
    
    async def test_real_upload_with_demo_credentials(self):
        """使用演示凭据测试上传流程（不会真正上传）"""
        try:
            print("\n🔄 开始今日头条上传集成测试...")
            
            # 1. 加载测试内容
            story_file = project_root / "tests" / "test-story.md"
            content_text = story_file.read_text(encoding='utf-8')
            
            # 创建内容对象
            content = Content(
                title="唐朝茶圣的秘密 - 集成测试",
                content=content_text,
                content_format=ContentFormat.MARKDOWN
            )
            
            self.log(f"内容加载成功: '{content.title}' ({len(content.content)}字符)")
            
            # 2. 转换内容
            transformed = await self.adapter.transform_content(content)
            self.log("✅ 内容转换完成")
            
            # 3. 验证内容
            validation = self.adapter._validate_format_impl(transformed)
            if not validation.is_valid:
                errors = [e.message for e in validation.errors]
                self.log(f"内容验证失败: {', '.join(errors)}", False)
                return False
            self.log("✅ 内容验证通过")
            
            # 4. 准备演示凭据（这些是无效的凭据，仅用于测试流程）
            demo_credentials = {
                "app_id": "demo_app_id",
                "secret": "demo_secret_key_32chars_long_enough_for_demo",
                "redirect_uri": "https://demo-app.com/callback"
            }
            
            # 5. 尝试认证（这将失败，因为我们没有真实的凭据）
            try:
                auth_result = await self.adapter.authenticate(demo_credentials)
                if auth_result.success:
                    self.log("✅ 认证成功（意外）")
                    
                    # 如果认证成功，尝试发布
                    try:
                        publish_result = await self.adapter.publish(transformed)
                        self.log(f"✅ 发布成功: {publish_result.get('url', 'N/A')}")
                        return True
                    except Exception as e:
                        self.log(f"❌ 发布失败: {e}", False)
                        return False
                else:
                    self.log(f"⚠️ 认证失败（预期）: {auth_result.error_message}")
            except Exception as e:
                self.log(f"⚠️ 认证过程出错（预期）: {e}")
            
            # 6. 模拟发布数据准备
            self.log("📋 模拟发布数据准备:")
            self.log(f"   标题: {transformed.title}")
            self.log(f"   内容长度: {len(transformed.html)}字符")
            self.log(f"   内容格式: {transformed.content_format}")
            
            # 7. 显示发布API端点和请求格式
            publish_url = f"{self.adapter.base_url}/media/article/publish/"
            self.log(f"   发布API端点: {publish_url}")
            
            # 8. 显示请求头格式
            auth_headers = self.adapter._get_auth_headers("demo_access_token")
            self.log(f"   请求头示例: {dict(list(auth_headers.items())[:3])}...")
            
            return True
            
        except Exception as e:
            self.log(f"集成测试失败: {e}", False)
            return False
    
    async def test_publish_error_handling(self):
        """测试发布错误处理"""
        try:
            # 创建测试内容
            test_content = TransformedContent(
                title="错误处理测试",
                content="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。",
                content_format=ContentFormat.HTML,
                html="<p>这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。</p>",
                text="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。"
            )
            
            # 尝试在没有认证的情况下发布
            try:
                result = await self.adapter._publish_impl(test_content)
                self.log("❌ 意外发布成功", False)
                return False
            except Exception as e:
                if "未找到有效的访问令牌" in str(e):
                    self.log("✅ 正确处理了认证缺失错误")
                    return True
                else:
                    self.log(f"❌ 未预期的错误: {e}", False)
                    return False
                    
        except Exception as e:
            self.log(f"错误处理测试失败: {e}", False)
            return False
    
    async def test_content_format_validation(self):
        """测试内容格式验证"""
        try:
            # 测试有效内容
            valid_content = TransformedContent(
                title="有效标题",
                content="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。",
                content_format=ContentFormat.HTML,
                html="<p>这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。</p>",
                text="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。"
            )
            
            result = self.adapter._validate_format_impl(valid_content)
            if result.is_valid:
                self.log("✅ 有效内容验证通过")
            else:
                self.log(f"❌ 有效内容验证失败: {[e.message for e in result.errors]}", False)
                return False
            
            # 测试无效内容（标题过长）
            invalid_content = TransformedContent(
                title="这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的标题",
                content="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。",
                content_format=ContentFormat.HTML,
                html="<p>这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。</p>",
                text="这是一个足够长的内容段落，包含超过100个字符的内容，用于测试今日头条适配器的内容验证功能。我们需要确保内容长度符合平台要求。这个内容长度应该超过100个字符，以确保能够通过验证。让我们再添加一些内容来确保长度足够。"
            )
            
            result = self.adapter._validate_format_impl(invalid_content)
            if not result.is_valid:
                self.log("✅ 无效内容验证通过")
            else:
                self.log("❌ 无效内容验证失败", False)
                return False
            
            return True
            
        except Exception as e:
            self.log(f"内容格式验证测试失败: {e}", False)
            return False
    
    async def run_all_tests(self):
        """运行所有集成测试"""
        print("🚀 开始今日头条上传集成测试\n")
        print("=" * 60)
        
        tests = [
            self.test_content_format_validation,
            self.test_publish_error_handling,
            self.test_real_upload_with_demo_credentials,
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log(f"测试执行出错: {e}", False)
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"\n📊 集成测试完成统计")
        print(f"总测试数: {len(tests)}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {failed}")
        print(f"通过率: {passed/len(tests)*100:.1f}%")
        
        if failed == 0:
            print("\n🎉 所有集成测试通过！")
            print("✅ 今日头条适配器已准备好进行实际发布测试")
            print("\n📝 实际发布需要:")
            print("1. 在今日头条开放平台注册应用")
            print("2. 获取有效的app_id和secret")
            print("3. 配置正确的redirect_uri")
            print("4. 完成OAuth认证流程")
        else:
            print(f"\n⚠️ 有 {failed} 个测试失败，需要修复后再进行实际发布")
        
        # 保存测试结果
        self.save_test_results()
        
        return failed == 0
    
    def save_test_results(self):
        """保存测试结果到文件"""
        import json
        
        results_dir = project_root / "test-results"
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = results_dir / f"toutiao-integration-test-{timestamp}.json"
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "success": all(r["success"] for r in self.results),
                "results": self.results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试结果已保存: {json_file}")


async def main():
    """主函数"""
    test = ToutiaoUploadIntegrationTest()
    success = await test.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
        sys.exit(1)